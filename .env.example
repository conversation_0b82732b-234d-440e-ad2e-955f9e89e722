# Server Configuration
PORT=8080
SERVER_HOST=0.0.0.0

# Database Configuration
DATABASE_URL=postgresql://postgres:your_password_here@localhost:5432/rockerstt?sslmode=disable

# Alternative individual database settings (if not using DATABASE_URL)
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password_here
DB_NAME=rockerstt
DB_SSLMODE=disable

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Token Configuration
HMAC_SECRET=your-secure-hmac-secret-key-here-minimum-32-characters
TOKEN_EXPIRY_MINS=5

# Apple Sign-In Configuration
APPLE_BUNDLE_ID=your.app.bundle.id
APPLE_JWK_URL=https://appleid.apple.com/auth/keys

# FunASR Configuration
FUNASR_URL=ws://localhost:10095

# Docker-specific environment variables
# These are set in the Dockerfile but can be overridden
# PORT=8080
# LOG_LEVEL=info
# TOKEN_EXPIRY_MINS=5
# APPLE_JWK_URL=https://appleid.apple.com/auth/keys
# FUNASR_URL=ws://localhost:10095