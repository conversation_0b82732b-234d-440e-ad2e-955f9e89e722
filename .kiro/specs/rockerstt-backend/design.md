# Design Document

## Overview

The RockerSTT Backend is a Go-based microservice that provides secure authentication and WebSocket proxying for real-time speech-to-text services. The system acts as a gateway between iOS clients and a FunASR server, implementing Apple Sign-In authentication, HMAC-based access control, and session tracking for billing purposes.

The architecture follows a clean separation of concerns with distinct layers for authentication, WebSocket proxying, and data persistence. The system is designed to be stateless, scalable, and production-ready with comprehensive logging and error handling.

## Architecture

```mermaid
graph TB
    subgraph "iOS Client"
        A[RockerSTT App]
    end
    
    subgraph "RockerSTT Backend"
        B[Gin HTTP Server]
        C[Auth Handler]
        D[WebSocket Gateway]
        E[Token Service]
        F[Session Service]
        G[Database Layer]
    end
    
    subgraph "External Services"
        H[Apple JWK Endpoint]
        I[FunASR Server<br/>ws://localhost:10095]
        J[PostgreSQL Database]
    end
    
    A -->|POST /api/token| B
    B --> C
    C --> E
    C -->|Validate Apple Token| H
    E --> G
    G --> J
    
    A -->|WebSocket /ws/asr| D
    D --> E
    D -->|Proxy Audio/Results| I
    D --> F
    F --> G
```

### Key Design Principles

1. **Security First**: All tokens are validated, secrets are environment-based, and no client data is trusted
2. **Stateless Design**: No server-side session storage, all state in tokens or database
3. **Fail-Safe**: Graceful error handling with proper HTTP status codes and logging
4. **Scalable**: Horizontal scaling support through stateless design
5. **Observable**: Comprehensive logging for debugging and monitoring

## Components and Interfaces

### 1. HTTP Server (Gin Framework)

**Purpose**: Main HTTP server handling REST API and WebSocket upgrade requests

**Key Responsibilities**:
- Route handling for `/api/token` and `/ws/asr`
- Middleware for CORS, logging, and error handling
- WebSocket upgrade handling
- Health check endpoints

**Interface**:
```go
type Server struct {
    router     *gin.Engine
    config     *Config
    authSvc    AuthService
    tokenSvc   TokenService
    sessionSvc SessionService
}

func (s *Server) Start(port string) error
func (s *Server) setupRoutes()
func (s *Server) handleToken(c *gin.Context)
func (s *Server) handleWebSocket(c *gin.Context)
```

### 2. Authentication Service

**Purpose**: Handles Apple Sign-In token validation and user management

**Key Responsibilities**:
- Fetch and cache Apple's JWK keys
- Validate Apple JWT tokens
- Extract user information from validated tokens
- Create/retrieve users in database

**Interface**:
```go
type AuthService interface {
    ValidateAppleToken(token string) (*AppleTokenClaims, error)
    GetOrCreateUser(appleUserID string) (*User, error)
}

type AppleTokenClaims struct {
    Sub   string `json:"sub"`
    Email string `json:"email"`
    Exp   int64  `json:"exp"`
    Iat   int64  `json:"iat"`
}
```

### 3. Token Service

**Purpose**: Generates and validates HMAC-signed access tokens

**Key Responsibilities**:
- Generate HMAC-SHA256 signed tokens with expiry
- Validate token signatures and expiry
- Parse token components (userID, timestamp, sessionID)

**Interface**:
```go
type TokenService interface {
    GenerateToken(userID string) (string, error)
    ValidateToken(token string) (*TokenClaims, error)
}

type TokenClaims struct {
    UserID    string
    Timestamp int64
    SessionID string
    Valid     bool
}
```

### 4. WebSocket Gateway

**Purpose**: Proxies WebSocket connections between clients and FunASR server

**Key Responsibilities**:
- Validate access tokens before establishing connections
- Establish bidirectional proxy to FunASR server
- Handle connection lifecycle and cleanup
- Forward audio data and transcription results

**Interface**:
```go
type WebSocketGateway struct {
    tokenSvc   TokenService
    sessionSvc SessionService
    funasrURL  string
}

func (w *WebSocketGateway) HandleConnection(c *gin.Context)
func (w *WebSocketGateway) proxyToFunASR(clientConn, funasrConn *websocket.Conn, sessionID string)
```

### 5. Session Service

**Purpose**: Manages session tracking for billing and analytics

**Key Responsibilities**:
- Create session records on WebSocket connection
- Update session records on disconnection
- Calculate session duration
- Store session metadata

**Interface**:
```go
type SessionService interface {
    StartSession(userID, sessionID string) error
    EndSession(sessionID string) error
    GetUserSessions(userID string) ([]Session, error)
}

type Session struct {
    SessionID    string
    UserID       string
    StartTime    time.Time
    EndTime      *time.Time
    DurationSecs *int
}
```

### 6. Database Layer

**Purpose**: Handles all database operations with proper connection management

**Key Responsibilities**:
- User CRUD operations
- Session CRUD operations
- Connection pooling and error handling
- Database migrations

**Interface**:
```go
type Database interface {
    GetUser(appleUserID string) (*User, error)
    CreateUser(user *User) error
    CreateSession(session *Session) error
    UpdateSession(sessionID string, endTime time.Time, duration int) error
}
```

## Data Models

### User Model
```go
type User struct {
    ID          uint      `gorm:"primaryKey"`
    AppleUserID string    `gorm:"uniqueIndex;not null"`
    Email       string    `gorm:"index"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
}
```

### Session Model
```go
type Session struct {
    SessionID    string     `gorm:"primaryKey;type:uuid"`
    UserID       string     `gorm:"not null;index"`
    StartTime    time.Time  `gorm:"not null"`
    EndTime      *time.Time
    DurationSecs *int
    CreatedAt    time.Time
    UpdatedAt    time.Time
}
```

### Configuration Model
```go
type Config struct {
    Port            string
    DatabaseURL     string
    HMACSecret      string
    AppleJWKURL     string
    FunASRURL       string
    TokenExpiryMins int
    LogLevel        string
}
```

## Error Handling

### HTTP Error Responses
```go
type ErrorResponse struct {
    Error   string `json:"error"`
    Code    string `json:"code"`
    Message string `json:"message"`
}
```

### Error Categories
1. **Authentication Errors** (401): Invalid Apple tokens, expired access tokens
2. **Authorization Errors** (403): Valid tokens but insufficient permissions
3. **Validation Errors** (400): Malformed requests, missing parameters
4. **Server Errors** (500): Database failures, external service failures
5. **WebSocket Errors**: Connection failures, proxy errors

### Error Handling Strategy
- Log all errors with appropriate levels (ERROR, WARN, INFO)
- Return user-friendly error messages without exposing internal details
- Implement circuit breaker pattern for external service calls
- Graceful degradation when possible

## Testing Strategy

### Unit Testing
- **Authentication Service**: Mock Apple JWK responses, test token validation
- **Token Service**: Test HMAC generation/validation, expiry handling
- **Session Service**: Test session lifecycle, duration calculations
- **Database Layer**: Use test database, test all CRUD operations

### Integration Testing
- **End-to-End API Testing**: Test complete authentication flow
- **WebSocket Testing**: Test proxy functionality with mock FunASR server
- **Database Integration**: Test with real PostgreSQL instance

### Load Testing
- **WebSocket Concurrency**: Test multiple simultaneous connections
- **Token Generation**: Test high-frequency token requests
- **Database Performance**: Test session logging under load

### Security Testing
- **Token Validation**: Test with malformed, expired, and tampered tokens
- **Input Validation**: Test with malicious payloads
- **Rate Limiting**: Test abuse scenarios

## Deployment Architecture

### Container Configuration
```dockerfile
FROM golang:1.21-alpine AS builder
# Build stage

FROM alpine:latest
# Runtime stage with minimal dependencies
```

### Environment Variables
```bash
PORT=8080
DATABASE_URL=********************************/db
HMAC_SECRET=your-secret-key
APPLE_JWK_URL=https://appleid.apple.com/auth/keys
FUNASR_URL=ws://localhost:10095
TOKEN_EXPIRY_MINS=5
LOG_LEVEL=info
```

### Reverse Proxy Configuration (Nginx)
```nginx
upstream rockerstt_backend {
    server backend:8080;
}

server {
    listen 443 ssl;
    server_name api.rockerstt.com;
    
    location /api/ {
        proxy_pass http://rockerstt_backend;
    }
    
    location /ws/ {
        proxy_pass http://rockerstt_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

### Health Checks
- **Liveness Probe**: `/health` endpoint checking server status
- **Readiness Probe**: `/ready` endpoint checking database connectivity
- **Metrics Endpoint**: `/metrics` for Prometheus monitoring

## Security Considerations

### Token Security
- HMAC secret stored in secure environment variables
- Tokens expire after 5 minutes to limit exposure
- No sensitive data stored in tokens

### Apple Token Validation
- Always validate signature using Apple's current JWK keys
- Check token expiry and issued-at times
- Validate audience and issuer claims

### WebSocket Security
- Token validation before WebSocket upgrade
- Connection timeout handling
- Rate limiting per user/IP

### Database Security
- Connection string with credentials in environment
- Prepared statements to prevent SQL injection
- Connection pooling with proper limits

### Logging Security
- No sensitive data (tokens, secrets) in logs
- Structured logging with appropriate levels
- Log rotation and retention policies