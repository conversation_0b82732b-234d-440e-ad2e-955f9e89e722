# Implementation Plan

- [x] 1. Set up project structure and core configuration
  - Create Go module with proper directory structure (cmd/, internal/, pkg/)
  - Implement configuration loading from environment variables
  - Set up logging with structured output using logrus or zap
  - Create basic health check endpoints
  - _Requirements: 4.4, 4.5, 6.4_

- [x] 2. Implement database layer and models
  - [x] 2.1 Create database models and connection setup
    - Define User and Session structs with GORM tags
    - Implement database connection with PostgreSQL using GORM
    - Create database interface with CRUD operations
    - Add connection pooling and error handling
    - _Requirements: 3.3, 4.4_

  - [x] 2.2 Implement database migrations and seed data
    - Create GORM auto-migration for User and Session tables
    - Write database initialization functions
    - Add proper indexes for performance (user_id, session_id)
    - Test database operations with unit tests
    - _Requirements: 3.3_

- [x] 3. Build authentication service for Apple Sign-In
  - [x] 3.1 Implement Apple JWK key fetching and caching
    - Create service to fetch Apple's public keys from JWK endpoint
    - Implement key caching with TTL refresh mechanism
    - Add error handling for network failures and invalid responses
    - Write unit tests with mocked HTTP responses
    - _Requirements: 1.1, 4.1_

  - [x] 3.2 Create Apple token validation logic
    - Implement JWT parsing and signature verification using Apple JWK keys
    - Extract user claims (sub, email, exp, iat) from validated tokens
    - Add token expiry and issuer validation
    - Write comprehensive unit tests for token validation scenarios
    - _Requirements: 1.1, 1.2, 4.1_

  - [x] 3.3 Implement user management operations
    - Create GetOrCreateUser function that checks existing users by Apple ID
    - Implement user creation in database for new Apple users
    - Add proper error handling for database operations
    - Write integration tests with test database
    - _Requirements: 1.3_

- [x] 4. Create HMAC token service for access control
  - [x] 4.1 Implement HMAC token generation
    - Create token structure with userID, timestamp, sessionID, and HMAC signature
    - Implement HMAC-SHA256 signing using secret from environment
    - Generate unique session IDs using UUID
    - Add token expiry validation (5-minute window)
    - _Requirements: 1.4, 4.2_

  - [x] 4.2 Build token validation and parsing
    - Implement token parsing to extract components (userID, timestamp, sessionID)
    - Create HMAC signature verification against stored secret
    - Add timestamp validation to ensure tokens are within 5-minute window
    - Write unit tests for valid and invalid token scenarios
    - _Requirements: 2.1, 2.2, 2.3, 4.2_

- [x] 5. Implement REST API endpoints
  - [x] 5.1 Create Gin HTTP server setup
    - Initialize Gin router with middleware for CORS, logging, and recovery
    - Set up route handlers for /api/token endpoint
    - Add request/response logging middleware
    - Implement graceful shutdown handling
    - _Requirements: 4.5, 6.1, 6.4_

  - [x] 5.2 Build POST /api/token authentication endpoint
    - Create request/response structs for token endpoint
    - Implement handler that accepts Apple identityToken from request body
    - Integrate Apple token validation and user creation/retrieval
    - Generate and return HMAC access token in response
    - Add comprehensive error handling with proper HTTP status codes
    - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 4.4, 6.1_

- [x] 6. Build WebSocket gateway and proxy functionality
  - [x] 6.1 Create WebSocket connection handler
    - Implement WebSocket upgrade handler for /ws/asr endpoint
    - Extract and validate token from query parameters
    - Reject connections with invalid or expired tokens
    - Set up proper WebSocket connection lifecycle management
    - _Requirements: 2.1, 2.2, 2.3, 6.2_

  - [x] 6.2 Implement WebSocket proxy to FunASR server
    - Create bidirectional proxy connection to ws://localhost:10095
    - Forward client audio data to FunASR server
    - Forward transcription results back to client
    - Handle connection errors and cleanup properly
    - _Requirements: 2.4, 2.5, 2.6_

  - [x] 6.3 Add session tracking for WebSocket connections
    - Create session record when WebSocket connection starts
    - Track session start time and generate session ID
    - Update session record with end time and duration when connection closes
    - Log session metadata for billing purposes
    - _Requirements: 2.7, 3.1, 3.2, 6.2, 6.3_

- [x] 7. Implement comprehensive error handling and logging
  - [x] 7.1 Create structured error handling system
    - Define error types for different failure scenarios (auth, validation, server)
    - Implement error response formatting with consistent JSON structure
    - Add proper HTTP status codes for different error types
    - Ensure no sensitive information is exposed in error messages
    - _Requirements: 4.6, 6.3_

  - [x] 7.2 Add comprehensive logging throughout the system
    - Log authentication attempts and outcomes with appropriate levels
    - Log WebSocket connection establishment and termination events
    - Log database operations and performance metrics
    - Add request/response logging for debugging
    - _Requirements: 6.1, 6.2, 6.3, 6.5_

- [x] 8. Create containerization and deployment configuration
  - [x] 8.1 Build Docker configuration
    - Create multi-stage Dockerfile with Go build and Alpine runtime
    - Set up proper file permissions and non-root user
    - Configure environment variable handling
    - Optimize image size and build time
    - _Requirements: 5.2, 5.4_

  - [x] 8.2 Add deployment and reverse proxy configuration
    - Create docker-compose.yml for local development with PostgreSQL
    - Write Nginx configuration for HTTPS and WebSocket proxying
    - Add environment variable templates and documentation
    - Create deployment scripts for cloud platforms
    - _Requirements: 4.5, 5.3, 5.4_

- [ ] 9. Write comprehensive tests
  - [x] 9.1 Create unit tests for core services
    - Write unit tests for AuthService with mocked Apple JWK responses
    - Create unit tests for TokenService covering generation and validation
    - Add unit tests for SessionService with mocked database operations
    - Test error scenarios and edge cases for all services
    - _Requirements: 1.1, 1.2, 1.4, 2.1, 2.2, 3.1, 3.2_

  - [x] 9.2 Build integration tests for API endpoints
    - Create integration tests for POST /api/token with real database
    - Write WebSocket integration tests with mock FunASR server
    - Test complete authentication and session tracking flow
    - Add tests for error scenarios and malformed requests
    - _Requirements: 1.5, 2.7, 4.6_

- [ ] 10. Add production readiness features
  - [x] 10.1 Implement health checks and monitoring
    - Create /health endpoint for liveness probes
    - Add /ready endpoint checking database connectivity
    - Implement /metrics endpoint for Prometheus monitoring
    - Add graceful shutdown with connection draining
    - _Requirements: 4.5, 6.4_

  - [x] 10.2 Add security hardening and rate limiting
    - Implement basic rate limiting per IP address
    - Add request timeout handling
    - Validate all input parameters and sanitize data
    - Add security headers and CORS configuration
    - _Requirements: 4.1, 4.3, 4.6_