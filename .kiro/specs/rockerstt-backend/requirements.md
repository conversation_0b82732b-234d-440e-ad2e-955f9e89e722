# Requirements Document

## Introduction

RockerSTT Backend is a Go-based authentication and WebSocket proxy server that sits between iOS clients and a FunASR real-time speech-to-text service. The system provides secure access control through Apple Sign-In authentication, issues short-lived access tokens, and proxies WebSocket connections to the FunASR server while tracking sessions for future billing capabilities. The backend ensures that only authenticated users can access the Cantonese transcription service running on ws://localhost:10095.

## Requirements

### Requirement 1

**User Story:** As an iOS app user, I want to authenticate using Sign in with Apple, so that I can securely access the speech-to-text service.

#### Acceptance Criteria

1. WHEN a client sends a POST request to /api/token with a valid Apple identityToken THEN the system SHALL validate the token using Apple's public keys (JWKs)
2. WHEN the Apple token validation succeeds THEN the system SHALL extract the user's unique ID (sub) from the token
3. WHEN a user authenticates for the first time THEN the system SHALL create a new user record in PostgreSQL database
4. WHEN a user authenticates successfully THEN the system SHALL issue an HMAC-signed access token with 5-minute validity containing userID, timestamp, sessionID, and signature
5. WHEN the token generation is complete THEN the system SHALL return the access token to the client

### Requirement 2

**User Story:** As an iOS app user, I want to establish a WebSocket connection for real-time speech transcription, so that I can get live Cantonese text conversion.

#### Acceptance Criteria

1. WHEN a client requests WebSocket upgrade to /ws/asr with a token parameter THEN the system SHALL validate the HMAC token signature
2. WHEN validating the HMAC token THEN the system SHALL check timestamp validity within 5 minutes
3. WHEN the HMAC token is invalid or expired THEN the system SHALL reject the WebSocket connection
4. WHEN the HMAC token is valid THEN the system SHALL establish a WebSocket proxy connection to ws://localhost:10095
5. WHEN audio data is received from the client THEN the system SHALL forward it to the FunASR server
6. WHEN transcription results are received from FunASR THEN the system SHALL forward them back to the client
7. WHEN the WebSocket connection closes THEN the system SHALL log session metadata including userID, start time, end time, and duration

### Requirement 3

**User Story:** As a system administrator, I want to track user sessions and usage data, so that I can implement billing and monitor system usage.

#### Acceptance Criteria

1. WHEN a WebSocket session starts THEN the system SHALL create a session record with session_id, user_id, and start_time
2. WHEN a WebSocket session ends THEN the system SHALL update the session record with end_time and calculate duration_secs
3. WHEN session data is stored THEN the system SHALL use PostgreSQL with proper data types (UUID for session_id, TEXT for user_id, TIMESTAMP for times, INTEGER for duration)
4. WHEN storing session data THEN the system SHALL ensure data integrity and handle database connection errors gracefully

### Requirement 4

**User Story:** As a system administrator, I want the backend to be secure and production-ready, so that user data is protected and the service is reliable.

#### Acceptance Criteria

1. WHEN handling Apple tokens THEN the system SHALL always validate token signature and expiry
2. WHEN generating HMAC tokens THEN the system SHALL use a secure secret stored in environment variables
3. WHEN processing client requests THEN the system SHALL not trust any client-provided information without verification
4. WHEN the system starts THEN the system SHALL load configuration from environment variables or config files
5. WHEN deployed THEN the system SHALL support HTTPS connections and proper reverse proxy configuration
6. WHEN errors occur THEN the system SHALL log appropriate error messages without exposing sensitive information

### Requirement 5

**User Story:** As a developer, I want the backend to be containerized and easily deployable, so that it can be deployed consistently across environments.

#### Acceptance Criteria

1. WHEN building the application THEN the system SHALL compile into a single Go binary
2. WHEN containerizing THEN the system SHALL include a Dockerfile for container deployment
3. WHEN deployed THEN the system SHALL accept incoming requests on configurable ports
4. WHEN running in production THEN the system SHALL support deployment on GCE, Cloud Run, or similar container platforms
5. WHEN configured THEN the system SHALL work behind reverse proxies like Nginx or Caddy

### Requirement 6

**User Story:** As a system operator, I want comprehensive logging and monitoring, so that I can troubleshoot issues and monitor system health.

#### Acceptance Criteria

1. WHEN processing requests THEN the system SHALL log authentication attempts and outcomes
2. WHEN WebSocket connections are established or terminated THEN the system SHALL log connection events
3. WHEN errors occur THEN the system SHALL log detailed error information for debugging
4. WHEN the system starts THEN the system SHALL log startup configuration and health status
5. WHEN database operations occur THEN the system SHALL log connection status and query performance metrics