# Product Overview

This workspace is configured for AI-assisted development using Kiro with Model Context Protocol (MCP) integration.

## Purpose
- Development workspace optimized for AI collaboration
- MCP server integration for enhanced AI capabilities
- VSCode integration with Kiro agent configuration

## Key Features
- MCP server configuration for external tool integration
- Kiro AI assistant steering and guidance system
- Development environment ready for AI-enhanced workflows