# Project Structure

## Directory Organization

```
.
├── .kiro/                    # Kiro AI assistant configuration
│   ├── settings/            # Configuration files
│   │   └── mcp.json        # MCP server definitions
│   └── steering/           # AI guidance documents
│       ├── product.md      # Product overview
│       ├── tech.md         # Technology stack
│       └── structure.md    # This file
└── .vscode/                # VSCode workspace settings
    └── settings.json       # IDE configuration
```

## Key Directories

### `.kiro/`
Central configuration directory for Kiro AI assistant:
- **`settings/`**: Configuration files for various Kiro features
- **`steering/`**: Markdown files that guide AI behavior and decision-making

### `.vscode/`
VSCode workspace configuration:
- Contains IDE-specific settings
- Enables Kiro agent integration

## File Conventions

### Steering Documents
- Use `.md` extension
- Place in `.kiro/steering/` directory
- Include front-matter for conditional inclusion if needed
- Reference external files using `#[[file:<relative_file_name>]]` syntax

### MCP Configuration
- JSON format in `.kiro/settings/mcp.json`
- Server definitions with command, args, and environment variables
- Auto-approval lists for trusted tools
- Disable/enable flags for individual servers

## Expansion Guidelines
When adding new project files:
- Keep Kiro configuration in `.kiro/` directory
- Use steering documents to guide AI on new conventions
- Update MCP configuration for new external tool integrations
- Maintain clear separation between AI configuration and project code