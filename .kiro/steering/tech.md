# Technology Stack

## Core Technologies
- **<PERSON>ro AI Assistant**: Primary development companion with autonomous capabilities
- **Model Context Protocol (MCP)**: External tool integration framework
- **VSCode**: Primary IDE with Kiro extension

## Configuration Files
- `.kiro/settings/mcp.json`: MCP server configurations
- `.vscode/settings.json`: VSCode workspace settings
- `.kiro/steering/*.md`: AI assistant guidance documents

## MCP Integration
- MCP servers can be added to extend AI capabilities
- Servers typically run via `uvx` command (requires `uv` Python package manager)
- Auto-approval settings available for trusted tools
- Servers reconnect automatically on config changes

## Common Commands
Since this is primarily a Kiro workspace, most operations are handled through the AI assistant. However, for MCP management:

```bash
# Install uv/uvx for MCP servers (if needed)
# Follow: https://docs.astral.sh/uv/getting-started/installation/

# MCP servers are managed through <PERSON><PERSON>'s MCP Server view
# Use Command Palette: "MCP" to find relevant commands
```

## Development Workflow
- Use Kiro for code generation, analysis, and modifications
- Leverage MCP servers for specialized external tool integration
- Configure steering documents to guide AI behavior
- Use hooks for automated workflows triggered by IDE events