# FunASR Integration Guide

This document describes the integration with an external FunASR server running on port 10096.

## Overview

The RockerSTT Backend has been configured to connect to an external FunASR server running on the host machine at port 10096. This setup allows the application to run in Docker containers while communicating with a FunASR server running outside of Docker.

## Configuration Changes

### 1. Updated Default Configuration

The default FunASR URL has been changed from `ws://localhost:10095` to `ws://host.docker.internal:10096`:

- **File**: `internal/config/config.go`
- **Change**: Default FUNASR_URL updated to use `host.docker.internal:10096`

### 2. Docker Configuration Updates

#### Dockerfile
- **File**: `Dockerfile`
- **Change**: Updated default FUNASR_URL environment variable to `ws://host.docker.internal:10096`

#### Docker Compose
- **File**: `docker-compose.yml`
- **Changes**:
  - Removed mock FunASR service
  - Updated FUNASR_URL to `ws://host.docker.internal:10096`
  - Added `extra_hosts` configuration to enable `host.docker.internal` resolution

#### Production Docker Compose
- **File**: `deploy/docker-compose.prod.yml`
- **Change**: Added `extra_hosts` configuration for production deployments

### 3. Environment Files

#### Development Environment
- **File**: `.env.docker`
- **Change**: Updated FUNASR_URL to `ws://host.docker.internal:10096`

#### Production Template
- **File**: `deploy/.env.production.template`
- **Change**: Updated default port to 10096 with documentation for local development

## Docker Networking

The application uses Docker's `extra_hosts` feature to enable communication with the host machine:

```yaml
extra_hosts:
  - "host.docker.internal:host-gateway"
```

This configuration allows Docker containers to reach services running on the host machine using the `host.docker.internal` hostname.

### Platform Compatibility

- **Docker Desktop (macOS/Windows)**: `host.docker.internal` is automatically available
- **Linux Docker**: The `extra_hosts` configuration maps `host.docker.internal` to the host gateway IP

## Running the Application

### Prerequisites

1. **FunASR Server**: Ensure your FunASR server is running on port 10096
2. **Docker**: Docker and Docker Compose installed
3. **Network Access**: Ensure port 10096 is accessible from Docker containers

### Development Setup

1. **Start the application**:
   ```bash
   docker-compose up -d
   ```

2. **Verify connectivity**:
   The application will attempt to connect to the FunASR server when WebSocket connections are established.

### Production Setup

1. **Configure environment**:
   ```bash
   cp deploy/.env.production.template .env.production
   # Edit .env.production with your specific FunASR server URL
   ```

2. **Deploy**:
   ```bash
   docker-compose -f deploy/docker-compose.prod.yml up -d
   ```

## Testing

### Comprehensive Test Suite

The integration includes extensive tests to validate the Docker setup and FunASR connectivity:

#### Configuration Tests
- **File**: `internal/config/config_test.go`
- **Coverage**: Configuration loading, defaults, environment variable overrides

#### Docker Tests
- **File**: `internal/docker/docker_test.go`
- **Coverage**: Docker build, compose validation, networking configuration

#### End-to-End Tests
- **File**: `test/docker_e2e_test.go`
- **Coverage**: Complete Docker setup validation, health checks, security configuration

#### Integration Tests
- **File**: `internal/server/websocket_integration_test.go`
- **Coverage**: WebSocket functionality with updated FunASR configuration

### Running Tests

```bash
# Run all tests
make test

# Run Docker-specific tests
go test -v ./internal/docker/

# Run configuration tests
go test -v ./internal/config/

# Run end-to-end Docker tests
go test -v ./test/
```

## Troubleshooting

### Common Issues

1. **Connection Refused**:
   - Verify FunASR server is running on port 10096
   - Check firewall settings
   - Ensure Docker has network access to host

2. **DNS Resolution**:
   - Verify `extra_hosts` configuration in docker-compose.yml
   - On Linux, ensure Docker daemon supports host-gateway

3. **Port Conflicts**:
   - Ensure port 10096 is not blocked by other services
   - Check if FunASR server is listening on the correct interface (0.0.0.0:10096)

### Debugging

1. **Check container logs**:
   ```bash
   docker-compose logs api
   ```

2. **Test connectivity from container**:
   ```bash
   docker-compose exec api wget -O- http://host.docker.internal:10096 || echo "Connection failed"
   ```

3. **Verify FunASR server**:
   ```bash
   # From host machine
   curl -I http://localhost:10096
   ```

## Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `FUNASR_URL` | `ws://host.docker.internal:10096` | WebSocket URL for FunASR server |
| `PORT` | `8080` | Application port |
| `LOG_LEVEL` | `info` | Logging level |

## Security Considerations

1. **Network Security**: The application connects to the host machine, ensure proper firewall rules
2. **Container Security**: Application runs as non-root user
3. **TLS**: Use `wss://` for production FunASR connections

## Migration from Mock FunASR

If migrating from the previous mock FunASR setup:

1. **Remove old containers**:
   ```bash
   docker-compose down
   docker-compose pull
   ```

2. **Update configuration**:
   The new configuration automatically points to the external FunASR server

3. **Restart services**:
   ```bash
   docker-compose up -d
   ```

## Monitoring

Monitor the application logs for FunASR connectivity:

```bash
# Watch for FunASR connection logs
docker-compose logs -f api | grep -i funasr
```

Successful connection logs will show:
```
INFO Connected to FunASR server, starting proxy
```

Failed connection logs will show:
```
ERROR Failed to connect to FunASR server
```
