# RockerSTT Backend

A Go-based backend service for the RockerSTT speech-to-text application.

## Project Structure

```
.
├── cmd/
│   └── server/          # Application entrypoints
├── internal/            # Private application code
│   ├── config/         # Configuration management
│   ├── logger/         # Structured logging
│   └── server/         # HTTP server and handlers
├── pkg/                # Public packages (for external use)
├── bin/                # Compiled binaries
├── .env.example        # Environment variables template
├── Makefile           # Build and development commands
└── go.mod             # Go module definition
```

## Getting Started

### Prerequisites

- Go 1.21 or higher
- Make (optional, for using Makefile commands)

### Installation

1. Clone the repository
2. Copy environment variables:
   ```bash
   cp .env.example .env
   ```
3. Install dependencies:
   ```bash
   go mod download
   ```

### Running the Application

#### Using Go directly:
```bash
go run cmd/server/main.go
```

#### Using Make:
```bash
make run
```

#### Building and running:
```bash
make build
./bin/server
```

### Configuration

The application is configured using environment variables. See `.env.example` for available options:

- `SERVER_HOST`: Server bind address (default: 0.0.0.0)
- `SERVER_PORT`: Server port (default: 8080)
- `LOG_LEVEL`: Logging level (debug, info, warn, error)
- `LOG_FORMAT`: Log format (json, text)

### Health Checks

The server provides several health check endpoints:

- `GET /health` - Basic health check with uptime
- `GET /health/ready` - Readiness probe
- `GET /health/live` - Liveness probe
- `GET /api/v1/status` - API status endpoint

### Development

#### Available Make commands:
- `make build` - Build the application
- `make run` - Run the application
- `make test` - Run tests
- `make clean` - Clean build artifacts
- `make deps` - Download dependencies
- `make fmt` - Format code

#### Hot reload development:
```bash
make dev-deps  # Install air for hot reload
make dev       # Run with hot reload
```

## Features

- ✅ Structured logging with JSON output
- ✅ Environment-based configuration
- ✅ Health check endpoints
- ✅ Graceful shutdown
- ✅ CORS middleware
- ✅ Request logging middleware
- ✅ Proper project structure following Go conventions