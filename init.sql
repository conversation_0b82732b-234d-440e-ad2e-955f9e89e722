-- Database initialization script for RockerSTT Backend
-- This script is run when the PostgreSQL container starts for the first time

-- Create the database if it doesn't exist (handled by POSTGRES_DB env var)
-- CREATE DATABASE IF NOT EXISTS rockerstt;

-- Connect to the rockerstt database
\c rockerstt;

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- The actual tables will be created by GORM auto-migration
-- This script is mainly for any initial setup or seed data

-- Create indexes that might not be handled by GORM
-- (These will be created by GORM migrations, but included here for reference)

-- Example seed data (uncomment if needed for development)
-- INSERT INTO users (apple_user_id, email, created_at, updated_at) 
-- VALUES ('dev.user.123', '<EMAIL>', NOW(), NOW())
-- ON CONFLICT (apple_user_id) DO NOTHING;

-- Grant permissions (if using different user)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO rockerstt_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO rockerstt_user;

-- Log initialization
SELECT 'RockerSTT database initialized successfully' as status;