#!/bin/bash

# validate-docker-setup.sh
# Script to validate the Docker setup for FunASR integration

set -e

echo "🔍 Validating Docker Setup for FunASR Integration"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        return 1
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "ℹ️  $1"
}

# Check if Docker is installed and running
echo ""
echo "1. Checking Docker installation..."
if command -v docker &> /dev/null; then
    print_status 0 "Docker is installed"
    
    if docker info &> /dev/null; then
        print_status 0 "Docker daemon is running"
    else
        print_status 1 "Docker daemon is not running"
        exit 1
    fi
else
    print_status 1 "Docker is not installed"
    exit 1
fi

# Check if Docker Compose is available
echo ""
echo "2. Checking Docker Compose..."
if command -v docker-compose &> /dev/null; then
    print_status 0 "Docker Compose is installed"
else
    print_status 1 "Docker Compose is not installed"
    exit 1
fi

# Validate Dockerfile
echo ""
echo "3. Validating Dockerfile..."
if [ -f "Dockerfile" ]; then
    print_status 0 "Dockerfile exists"
    
    # Check for updated FunASR URL
    if grep -q "FUNASR_URL=ws://host.docker.internal:10096" Dockerfile; then
        print_status 0 "Dockerfile contains updated FunASR URL"
    else
        print_status 1 "Dockerfile does not contain updated FunASR URL"
    fi
    
    # Check for security best practices
    if grep -q "USER appuser" Dockerfile; then
        print_status 0 "Dockerfile runs as non-root user"
    else
        print_status 1 "Dockerfile should run as non-root user"
    fi
else
    print_status 1 "Dockerfile not found"
fi

# Validate docker-compose.yml
echo ""
echo "4. Validating docker-compose.yml..."
if [ -f "docker-compose.yml" ]; then
    print_status 0 "docker-compose.yml exists"
    
    # Validate YAML syntax
    if docker-compose config &> /dev/null; then
        print_status 0 "docker-compose.yml syntax is valid"
    else
        print_status 1 "docker-compose.yml has syntax errors"
    fi
    
    # Check for updated FunASR URL
    if grep -q "host.docker.internal:10096" docker-compose.yml; then
        print_status 0 "docker-compose.yml contains updated FunASR URL"
    else
        print_status 1 "docker-compose.yml does not contain updated FunASR URL"
    fi
    
    # Check for extra_hosts configuration
    if grep -q "extra_hosts:" docker-compose.yml; then
        print_status 0 "docker-compose.yml has extra_hosts configuration"
    else
        print_status 1 "docker-compose.yml missing extra_hosts configuration"
    fi
    
    # Check that mock FunASR service is removed
    if grep -q "funasr:" docker-compose.yml; then
        print_status 1 "docker-compose.yml still contains mock FunASR service"
    else
        print_status 0 "Mock FunASR service removed from docker-compose.yml"
    fi
else
    print_status 1 "docker-compose.yml not found"
fi

# Validate production docker-compose
echo ""
echo "5. Validating production docker-compose..."
if [ -f "deploy/docker-compose.prod.yml" ]; then
    print_status 0 "Production docker-compose.yml exists"
    
    # Check for extra_hosts configuration
    if grep -q "extra_hosts:" deploy/docker-compose.prod.yml; then
        print_status 0 "Production docker-compose.yml has extra_hosts configuration"
    else
        print_status 1 "Production docker-compose.yml missing extra_hosts configuration"
    fi
else
    print_status 1 "Production docker-compose.yml not found"
fi

# Validate environment files
echo ""
echo "6. Validating environment files..."
if [ -f ".env.docker" ]; then
    print_status 0 ".env.docker exists"
    
    if grep -q "host.docker.internal:10096" .env.docker; then
        print_status 0 ".env.docker contains updated FunASR URL"
    else
        print_status 1 ".env.docker does not contain updated FunASR URL"
    fi
else
    print_warning ".env.docker not found (optional)"
fi

if [ -f "deploy/.env.production.template" ]; then
    print_status 0 "Production environment template exists"
    
    if grep -q "10096" deploy/.env.production.template; then
        print_status 0 "Production template references port 10096"
    else
        print_status 1 "Production template does not reference port 10096"
    fi
else
    print_status 1 "Production environment template not found"
fi

# Test Docker build
echo ""
echo "7. Testing Docker build..."
print_info "Building Docker image (this may take a few minutes)..."

if docker build -t rockerstt-backend:validation-test . &> /dev/null; then
    print_status 0 "Docker image builds successfully"
    
    # Clean up test image
    docker rmi rockerstt-backend:validation-test &> /dev/null || true
else
    print_status 1 "Docker build failed"
fi

# Check for FunASR server
echo ""
echo "8. Checking FunASR server connectivity..."
print_info "Testing connectivity to FunASR server on port 10096..."

# Test different possible FunASR URLs
FUNASR_URLS=("localhost:10096" "127.0.0.1:10096")
FUNASR_FOUND=false

for url in "${FUNASR_URLS[@]}"; do
    if timeout 3 bash -c "</dev/tcp/${url%:*}/${url#*:}" 2>/dev/null; then
        print_status 0 "FunASR server is accessible at $url"
        FUNASR_FOUND=true
        break
    fi
done

if [ "$FUNASR_FOUND" = false ]; then
    print_warning "FunASR server not accessible on port 10096"
    print_info "This is expected if FunASR server is not currently running"
    print_info "Make sure to start your FunASR server before running the application"
fi

# Validate Makefile
echo ""
echo "9. Validating Makefile..."
if [ -f "Makefile" ]; then
    print_status 0 "Makefile exists"
    
    # Check for Docker targets
    if grep -q "docker-build:" Makefile; then
        print_status 0 "Makefile contains Docker build target"
    else
        print_status 1 "Makefile missing Docker build target"
    fi
    
    if grep -q "test-docker:" Makefile; then
        print_status 0 "Makefile contains Docker test target"
    else
        print_status 1 "Makefile missing Docker test target"
    fi
else
    print_status 1 "Makefile not found"
fi

# Summary
echo ""
echo "=================================================="
echo "🎯 Validation Summary"
echo "=================================================="

print_info "Configuration files have been updated for FunASR integration"
print_info "Docker setup is configured to connect to external FunASR server"
print_info "Application will connect to FunASR at ws://host.docker.internal:10096"

echo ""
echo "📋 Next Steps:"
echo "1. Ensure your FunASR server is running on port 10096"
echo "2. Start the application: docker-compose up -d"
echo "3. Check logs: docker-compose logs -f api"
echo "4. Test WebSocket connection to /ws/asr endpoint"

echo ""
echo "🔧 Troubleshooting:"
echo "- If connection fails, verify FunASR server is accessible"
echo "- Check firewall settings for port 10096"
echo "- Review application logs for connection errors"

echo ""
echo "10. Testing Docker Compose deployment..."
print_info "Testing that containers start successfully..."

# Test docker-compose up
if docker-compose ps | grep -q "Up"; then
    print_status 0 "Docker containers are running successfully"

    # Test API health endpoint
    if docker-compose exec -T api wget -qO- http://localhost:8080/health > /dev/null 2>&1; then
        print_status 0 "API health endpoint is responding"
    else
        print_status 1 "API health endpoint is not responding"
    fi

    # Test API status endpoint
    if docker-compose exec -T api wget -qO- http://localhost:8080/api/v1/status > /dev/null 2>&1; then
        print_status 0 "API status endpoint is responding"
    else
        print_status 1 "API status endpoint is not responding"
    fi
else
    print_warning "Docker containers are not currently running"
    print_info "Run 'docker-compose up -d' to start the application"
fi

echo ""
print_status 0 "Docker setup validation completed!"
