.PHONY: build run test clean deps

# Build the application
build:
	go build -o bin/server cmd/server/main.go

# Run the application
run:
	go run cmd/server/main.go

# Run tests
test:
	go test -v ./...

# Clean build artifacts
clean:
	rm -rf bin/

# Download dependencies
deps:
	go mod download
	go mod tidy

# Install development dependencies
dev-deps: deps
	go install github.com/air-verse/air@latest

# Run with hot reload (requires air)
dev:
	air

# Format code
fmt:
	go fmt ./...

# Lint code (requires golangci-lint)
lint:
	golangci-lint run

# Build for production
build-prod:
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/server cmd/server/main.go

# Docker commands
docker-build:
	docker build -t rockerstt-backend:latest .

docker-build-no-cache:
	docker build --no-cache -t rockerstt-backend:latest .

docker-run:
	docker run --rm -p 8080:8080 --env-file .env rockerstt-backend:latest

docker-run-detached:
	docker run -d -p 8080:8080 --env-file .env --name rockerstt-backend rockerstt-backend:latest

docker-stop:
	docker stop rockerstt-backend || true
	docker rm rockerstt-backend || true

docker-logs:
	docker logs -f rockerstt-backend

# Multi-platform build (for production deployment)
docker-buildx:
	docker buildx build --platform linux/amd64,linux/arm64 -t rockerstt-backend:latest .

# Deployment commands
deploy-dev:
	./deploy/deploy.sh development

deploy-prod:
	./deploy/deploy.sh production

deploy-stop:
	./deploy/deploy.sh stop

deploy-clean:
	./deploy/deploy.sh clean

# Cloud deployments
deploy-gcp:
	./deploy/cloud-run-deploy.sh

# Test deployment
test-docker:
	./deploy/deploy.sh test