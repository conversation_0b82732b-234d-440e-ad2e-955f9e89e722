package test

import (
	"bufio"
	"context"
	"fmt"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestDockerEndToEnd tests the complete Docker setup end-to-end
func TestDockerEndToEnd(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping end-to-end Docker test in short mode")
	}
	
	// Get project root directory
	projectRoot, err := getProjectRoot()
	require.NoError(t, err, "Should be able to find project root")
	
	t.Run("DockerBuildSuccess", func(t *testing.T) {
		testDockerBuild(t, projectRoot)
	})
	
	t.Run("DockerComposeValidation", func(t *testing.T) {
		testDockerComposeValidation(t, projectRoot)
	})
	
	t.Run("EnvironmentConfiguration", func(t *testing.T) {
		testEnvironmentConfiguration(t, projectRoot)
	})
}

// testDockerBuild tests that the Docker image builds successfully
func testDockerBuild(t *testing.T, projectRoot string) {
	// Build the Docker image
	cmd := exec.Command("docker", "build", "-t", "rockerstt-backend:e2e-test", ".")
	cmd.Dir = projectRoot
	
	// Capture output for debugging
	output, err := cmd.CombinedOutput()
	if err != nil {
		t.Logf("Docker build output:\n%s", string(output))
		t.Fatalf("Docker build failed: %v", err)
	}
	
	t.Logf("Docker build completed successfully")
	
	// Verify the image was created
	cmd = exec.Command("docker", "images", "rockerstt-backend:e2e-test", "--format", "{{.Repository}}:{{.Tag}}")
	output, err = cmd.Output()
	require.NoError(t, err, "Failed to list Docker images")
	
	imageList := strings.TrimSpace(string(output))
	assert.Contains(t, imageList, "rockerstt-backend:e2e-test", "Docker image should be created")
	
	// Clean up the test image
	t.Cleanup(func() {
		cleanupCmd := exec.Command("docker", "rmi", "rockerstt-backend:e2e-test")
		cleanupCmd.Run() // Ignore errors during cleanup
	})
}

// testDockerComposeValidation tests Docker Compose configuration
func testDockerComposeValidation(t *testing.T, projectRoot string) {
	// Test docker-compose config validation
	cmd := exec.Command("docker-compose", "config")
	cmd.Dir = projectRoot
	
	output, err := cmd.CombinedOutput()
	if err != nil {
		t.Logf("Docker Compose config output:\n%s", string(output))
		t.Fatalf("Docker Compose config validation failed: %v", err)
	}
	
	configOutput := string(output)
	
	// Verify expected services are present
	assert.Contains(t, configOutput, "api:", "Config should contain API service")
	assert.Contains(t, configOutput, "db:", "Config should contain database service")
	assert.Contains(t, configOutput, "nginx:", "Config should contain nginx service")
	
	// Verify mock FunASR service is not present
	assert.NotContains(t, configOutput, "funasr:", "Config should not contain mock FunASR service")
	
	// Verify FunASR URL configuration
	assert.Contains(t, configOutput, "host.docker.internal:10096", 
		"Config should contain updated FunASR URL")
	
	// Verify extra_hosts configuration
	assert.Contains(t, configOutput, "extra_hosts:", "Config should contain extra_hosts")
	assert.Contains(t, configOutput, "host.docker.internal:host-gateway", 
		"Config should map host.docker.internal to host gateway")
}

// testEnvironmentConfiguration tests environment file configurations
func testEnvironmentConfiguration(t *testing.T, projectRoot string) {
	envFiles := []string{
		".env.docker",
		"deploy/.env.production.template",
	}
	
	for _, envFile := range envFiles {
		t.Run(fmt.Sprintf("Testing_%s", strings.ReplaceAll(envFile, "/", "_")), func(t *testing.T) {
			envPath := filepath.Join(projectRoot, envFile)
			content, err := os.ReadFile(envPath)
			require.NoError(t, err, "Environment file should be readable")
			
			envContent := string(content)
			
			// Check for updated FunASR configuration
			assert.Contains(t, envContent, "FUNASR_URL", "Environment file should contain FUNASR_URL")
			assert.Contains(t, envContent, "10096", "Environment file should reference port 10096")
			
			// For Docker environment file, verify host.docker.internal
			if strings.Contains(envFile, ".env.docker") {
				assert.Contains(t, envContent, "host.docker.internal", 
					"Docker environment file should use host.docker.internal")
			}
		})
	}
}

// TestDockerNetworkConnectivity tests Docker networking setup
func TestDockerNetworkConnectivity(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping Docker network connectivity test in short mode")
	}
	
	// This test verifies that the Docker networking configuration is correct
	// It doesn't require an actual FunASR server to be running
	
	projectRoot, err := getProjectRoot()
	require.NoError(t, err)
	
	t.Run("ExtraHostsConfiguration", func(t *testing.T) {
		// Verify docker-compose.yml has extra_hosts configuration
		dockerComposePath := filepath.Join(projectRoot, "docker-compose.yml")
		content, err := os.ReadFile(dockerComposePath)
		require.NoError(t, err)
		
		configContent := string(content)
		assert.Contains(t, configContent, "extra_hosts:", "Docker compose should have extra_hosts")
		assert.Contains(t, configContent, "host.docker.internal:host-gateway", 
			"Docker compose should map host.docker.internal")
	})
	
	t.Run("ProductionExtraHostsConfiguration", func(t *testing.T) {
		// Verify production docker-compose.yml has extra_hosts configuration
		prodComposePath := filepath.Join(projectRoot, "deploy", "docker-compose.prod.yml")
		content, err := os.ReadFile(prodComposePath)
		require.NoError(t, err)
		
		configContent := string(content)
		assert.Contains(t, configContent, "extra_hosts:", "Production docker compose should have extra_hosts")
		assert.Contains(t, configContent, "host.docker.internal:host-gateway", 
			"Production docker compose should map host.docker.internal")
	})
}

// TestDockerHealthChecks tests that health checks are properly configured
func TestDockerHealthChecks(t *testing.T) {
	projectRoot, err := getProjectRoot()
	require.NoError(t, err)
	
	t.Run("DockerfileHealthCheck", func(t *testing.T) {
		dockerfilePath := filepath.Join(projectRoot, "Dockerfile")
		content, err := os.ReadFile(dockerfilePath)
		require.NoError(t, err)
		
		dockerfileContent := string(content)
		assert.Contains(t, dockerfileContent, "HEALTHCHECK", "Dockerfile should contain health check")
		assert.Contains(t, dockerfileContent, "/health", "Health check should use /health endpoint")
	})
	
	t.Run("DockerComposeHealthCheck", func(t *testing.T) {
		dockerComposePath := filepath.Join(projectRoot, "docker-compose.yml")
		content, err := os.ReadFile(dockerComposePath)
		require.NoError(t, err)
		
		configContent := string(content)
		assert.Contains(t, configContent, "healthcheck:", "Docker compose should have health checks")
		assert.Contains(t, configContent, "/health", "Health check should use /health endpoint")
	})
}

// TestMakefileDockerIntegration tests Makefile Docker commands
func TestMakefileDockerIntegration(t *testing.T) {
	projectRoot, err := getProjectRoot()
	require.NoError(t, err)
	
	makefilePath := filepath.Join(projectRoot, "Makefile")
	content, err := os.ReadFile(makefilePath)
	require.NoError(t, err)
	
	makefileContent := string(content)
	
	// Verify Docker-related targets exist
	dockerTargets := []string{
		"docker-build:",
		"docker-run:",
		"docker-build-no-cache:",
		"docker-run-detached:",
		"docker-stop:",
		"test-docker:",
	}
	
	for _, target := range dockerTargets {
		assert.Contains(t, makefileContent, target, 
			fmt.Sprintf("Makefile should contain %s target", target))
	}
	
	// Verify correct image name is used
	assert.Contains(t, makefileContent, "rockerstt-backend:latest", 
		"Makefile should reference correct image name")
}

// TestDockerIgnore tests that .dockerignore is properly configured
func TestDockerIgnore(t *testing.T) {
	projectRoot, err := getProjectRoot()
	require.NoError(t, err)
	
	dockerIgnorePath := filepath.Join(projectRoot, ".dockerignore")
	
	// Check if .dockerignore exists
	if _, err := os.Stat(dockerIgnorePath); os.IsNotExist(err) {
		t.Skip(".dockerignore file does not exist, skipping test")
		return
	}
	
	content, err := os.ReadFile(dockerIgnorePath)
	require.NoError(t, err, ".dockerignore should be readable")
	
	dockerIgnoreContent := string(content)
	
	// Verify common patterns are ignored
	commonPatterns := []string{
		"*.md",
		".git",
		"bin/",
		"*.log",
	}
	
	for _, pattern := range commonPatterns {
		if !strings.Contains(dockerIgnoreContent, pattern) {
			t.Logf("Consider adding '%s' to .dockerignore for better build performance", pattern)
		}
	}
}

// getProjectRoot finds the project root directory
func getProjectRoot() (string, error) {
	// Start from current directory and walk up to find go.mod
	dir, err := os.Getwd()
	if err != nil {
		return "", err
	}
	
	for {
		goModPath := filepath.Join(dir, "go.mod")
		if _, err := os.Stat(goModPath); err == nil {
			return dir, nil
		}
		
		parent := filepath.Dir(dir)
		if parent == dir {
			// Reached root directory
			break
		}
		dir = parent
	}
	
	return "", fmt.Errorf("could not find project root (go.mod not found)")
}

// TestDockerSecurityConfiguration tests Docker security best practices
func TestDockerSecurityConfiguration(t *testing.T) {
	projectRoot, err := getProjectRoot()
	require.NoError(t, err)
	
	dockerfilePath := filepath.Join(projectRoot, "Dockerfile")
	content, err := os.ReadFile(dockerfilePath)
	require.NoError(t, err)
	
	dockerfileContent := string(content)
	
	// Verify non-root user is used
	assert.Contains(t, dockerfileContent, "USER appuser", "Dockerfile should run as non-root user")
	
	// Verify proper user creation
	assert.Contains(t, dockerfileContent, "adduser", "Dockerfile should create dedicated user")
	
	// Verify proper file permissions
	assert.Contains(t, dockerfileContent, "chmod +x", "Dockerfile should set proper executable permissions")
}
