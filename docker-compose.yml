services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: rockerstt-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: rockerstt
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d rockerstt"]
      interval: 10s
      timeout: 5s
      retries: 5

  # RockerSTT Backend API
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rockerstt-api
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=**************************************/rockerstt?sslmode=disable
      - HMAC_SECRET=development-secret-key-change-in-production-minimum-32-chars
      - TOKEN_EXPIRY_MINS=5
      - APPLE_JWK_URL=https://appleid.apple.com/auth/keys
      - FUNASR_URL=ws://funasr:10095
      - LOG_LEVEL=info
      - PORT=8080
    depends_on:
      db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # FunASR Server (Mock for development)
  funasr:
    image: alpine:latest
    container_name: rockerstt-funasr
    restart: unless-stopped
    ports:
      - "10095:10095"
    command: >
      sh -c "
        echo 'Mock FunASR server - replace with actual FunASR container';
        echo 'Listening on port 10095...';
        while true; do sleep 30; done
      "
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "10095"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: rockerstt-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local

networks:
  default:
    name: rockerstt-network