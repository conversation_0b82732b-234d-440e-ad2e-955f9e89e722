# Production Environment Variables Template
# Copy this file to .env.production and update the values

# Server Configuration
PORT=8080

# Database Configuration
# Use a managed PostgreSQL service like Google Cloud SQL, AWS RDS, or Azure Database
DATABASE_URL=****************************************/database?sslmode=require

# Security Configuration
# Generate a secure random string (minimum 32 characters)
# Example: openssl rand -base64 32
HMAC_SECRET=your-production-hmac-secret-key-minimum-32-characters

# Token Configuration
TOKEN_EXPIRY_MINS=5

# Apple Sign-In Configuration
APPLE_BUNDLE_ID=your.production.app.bundle.id
APPLE_JWK_URL=https://appleid.apple.com/auth/keys

# FunASR Configuration
# Update this to point to your production FunASR server
# For local development with external FunASR server, use: ws://host.docker.internal:10096
FUNASR_URL=wss://your-funasr-server.com:10096

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# SSL Configuration (for nginx)
SSL_CERT_PATH=/path/to/ssl/certificates

# Cloud-specific configurations
# Uncomment and configure based on your cloud provider

# Google Cloud
# GOOGLE_CLOUD_PROJECT=your-project-id
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# AWS
# AWS_REGION=us-west-2
# AWS_ACCESS_KEY_ID=your-access-key
# AWS_SECRET_ACCESS_KEY=your-secret-key

# Azure
# AZURE_SUBSCRIPTION_ID=your-subscription-id
# AZURE_RESOURCE_GROUP=your-resource-group