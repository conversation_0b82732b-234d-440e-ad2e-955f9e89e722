# RockerSTT Backend Deployment Guide

This directory contains deployment configurations and scripts for the RockerSTT Backend service.

## Quick Start

### Local Development

1. **Start with Docker Compose:**
   ```bash
   # From the project root
   docker-compose up -d
   ```

2. **Access the services:**
   - API: http://localhost:8080
   - Database: localhost:5432
   - Nginx: http://localhost

### Production Deployment

1. **Copy environment template:**
   ```bash
   cp deploy/.env.production.template .env.production
   ```

2. **Update environment variables** in `.env.production`

3. **Deploy using Docker Compose:**
   ```bash
   docker-compose -f deploy/docker-compose.prod.yml up -d
   ```

## Deployment Options

### 1. Docker Compose (Recommended for VPS/Dedicated Servers)

**Files:**
- `docker-compose.yml` - Development environment
- `deploy/docker-compose.prod.yml` - Production environment
- `nginx.conf` - Development Nginx configuration
- `deploy/nginx.prod.conf` - Production Nginx configuration

**Steps:**
```bash
# Build the application image
docker build -t rockerstt-backend:latest .

# Start production services
docker-compose -f deploy/docker-compose.prod.yml up -d

# Check service status
docker-compose -f deploy/docker-compose.prod.yml ps

# View logs
docker-compose -f deploy/docker-compose.prod.yml logs -f api
```

### 2. Google Cloud Run

**Files:**
- `deploy/cloud-run-deploy.sh` - Automated deployment script

**Prerequisites:**
- Google Cloud SDK installed
- Docker installed
- Project with billing enabled

**Steps:**
```bash
# Make script executable
chmod +x deploy/cloud-run-deploy.sh

# Deploy (replace with your project ID and region)
./deploy/cloud-run-deploy.sh your-project-id us-central1
```

**Manual Cloud Run Deployment:**
```bash
# Build and push image
docker build -t gcr.io/your-project/rockerstt-backend .
docker push gcr.io/your-project/rockerstt-backend

# Deploy to Cloud Run
gcloud run deploy rockerstt-backend \
  --image gcr.io/your-project/rockerstt-backend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --port 8080 \
  --memory 512Mi \
  --set-env-vars "LOG_LEVEL=info"
```

### 3. AWS ECS/Fargate

**Steps:**
```bash
# Create ECR repository
aws ecr create-repository --repository-name rockerstt-backend

# Get login token
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 123456789012.dkr.ecr.us-west-2.amazonaws.com

# Build and push
docker build -t rockerstt-backend .
docker tag rockerstt-backend:latest 123456789012.dkr.ecr.us-west-2.amazonaws.com/rockerstt-backend:latest
docker push 123456789012.dkr.ecr.us-west-2.amazonaws.com/rockerstt-backend:latest

# Create ECS task definition and service (use AWS Console or CLI)
```

### 4. Kubernetes

**Example Deployment:**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rockerstt-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: rockerstt-backend
  template:
    metadata:
      labels:
        app: rockerstt-backend
    spec:
      containers:
      - name: rockerstt-backend
        image: rockerstt-backend:latest
        ports:
        - containerPort: 8080
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: rockerstt-secrets
              key: database-url
        - name: HMAC_SECRET
          valueFrom:
            secretKeyRef:
              name: rockerstt-secrets
              key: hmac-secret
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

## Environment Variables

### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `********************************/db?sslmode=require` |
| `HMAC_SECRET` | Secret key for token signing (min 32 chars) | `your-secure-secret-key-here` |

### Optional Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `PORT` | `8080` | Server port |
| `LOG_LEVEL` | `info` | Logging level (debug, info, warn, error) |
| `TOKEN_EXPIRY_MINS` | `5` | Access token expiry in minutes |
| `APPLE_JWK_URL` | `https://appleid.apple.com/auth/keys` | Apple JWK endpoint |
| `FUNASR_URL` | `ws://localhost:10095` | FunASR WebSocket URL |

## SSL/TLS Configuration

### Development
- Uses self-signed certificates or HTTP
- Nginx configuration in `nginx.conf`

### Production
- Requires valid SSL certificates
- Place certificates in `/etc/nginx/ssl/` or mount as volume
- Update `deploy/nginx.prod.conf` with correct paths

### Let's Encrypt (Recommended)
```bash
# Install certbot
sudo apt-get install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal (add to crontab)
0 12 * * * /usr/bin/certbot renew --quiet
```

## Database Setup

### Development
- Uses PostgreSQL container from docker-compose
- Database: `rockerstt`
- User/Password: `postgres/postgres`

### Production Options

1. **Managed Database Services:**
   - Google Cloud SQL
   - AWS RDS
   - Azure Database for PostgreSQL
   - DigitalOcean Managed Databases

2. **Self-hosted PostgreSQL:**
   ```bash
   # Install PostgreSQL
   sudo apt-get install postgresql postgresql-contrib
   
   # Create database and user
   sudo -u postgres createdb rockerstt
   sudo -u postgres createuser rockerstt_user
   sudo -u postgres psql -c "ALTER USER rockerstt_user WITH PASSWORD 'secure_password';"
   sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE rockerstt TO rockerstt_user;"
   ```

## Monitoring and Logging

### Health Checks
- **Endpoint:** `/health`
- **Response:** `{"status": "ok"}`
- **Use for:** Load balancer health checks, container orchestration

### Metrics
- **Endpoint:** `/metrics` (restricted to internal networks)
- **Format:** Prometheus format
- **Use for:** Monitoring and alerting

### Logging
- Structured JSON logging
- Configurable log levels
- Container logs available via `docker logs`

## Security Considerations

### Network Security
- Use HTTPS in production
- Restrict database access to application servers only
- Use VPC/private networks when possible

### Secrets Management
- Never commit secrets to version control
- Use environment variables or secret management services
- Rotate secrets regularly

### Rate Limiting
- Nginx configuration includes rate limiting
- API: 5 requests/second per IP
- WebSocket: 2 connections/second per IP

### CORS Configuration
- Configured for cross-origin requests
- Update `nginx.conf` for specific domains in production

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check database connectivity
   docker-compose exec api sh -c 'nc -z db 5432'
   
   # Check environment variables
   docker-compose exec api env | grep DATABASE
   ```

2. **SSL Certificate Issues**
   ```bash
   # Check certificate validity
   openssl x509 -in /path/to/cert.pem -text -noout
   
   # Test SSL connection
   openssl s_client -connect your-domain.com:443
   ```

3. **WebSocket Connection Issues**
   ```bash
   # Check if WebSocket upgrade is working
   curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Sec-WebSocket-Version: 13" -H "Sec-WebSocket-Key: test" http://localhost/ws/asr?token=test
   ```

### Logs and Debugging

```bash
# View application logs
docker-compose logs -f api

# View nginx logs
docker-compose logs -f nginx

# View database logs
docker-compose logs -f db

# Enter container for debugging
docker-compose exec api sh
```

## Performance Tuning

### Application
- Adjust `TOKEN_EXPIRY_MINS` based on usage patterns
- Monitor database connection pool settings
- Use connection keep-alive for WebSocket connections

### Nginx
- Tune worker processes and connections
- Adjust buffer sizes based on request patterns
- Enable HTTP/2 for better performance

### Database
- Create appropriate indexes
- Monitor query performance
- Use connection pooling
- Consider read replicas for high traffic

## Backup and Recovery

### Database Backup
```bash
# Create backup
docker-compose exec db pg_dump -U postgres rockerstt > backup.sql

# Restore backup
docker-compose exec -T db psql -U postgres rockerstt < backup.sql
```

### Application Data
- Session data is stored in database
- No persistent application state
- Backup environment variables and configuration files

## Scaling

### Horizontal Scaling
- Application is stateless and can be scaled horizontally
- Use load balancer to distribute traffic
- Database may need scaling (read replicas, sharding)

### Vertical Scaling
- Increase container resources (CPU, memory)
- Monitor resource usage and adjust accordingly
- Database may need more powerful instance

## Support

For deployment issues:
1. Check the logs for error messages
2. Verify environment variables are set correctly
3. Ensure all required services are running
4. Check network connectivity between services
5. Verify SSL certificates are valid and properly configured