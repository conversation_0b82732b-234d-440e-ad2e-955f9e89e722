#!/bin/bash

# General deployment script for RockerSTT Backend
# Usage: ./deploy/deploy.sh [environment]

set -e

ENVIRONMENT=${1:-"development"}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "🚀 Deploying RockerSTT Backend"
echo "Environment: $ENVIRONMENT"
echo "Project Root: $PROJECT_ROOT"

cd "$PROJECT_ROOT"

case $ENVIRONMENT in
    "development"|"dev")
        echo "📋 Starting development environment..."
        
        # Check if .env exists, if not copy from example
        if [ ! -f .env ]; then
            echo "📝 Creating .env from .env.example..."
            cp .env.example .env
            echo "⚠️  Please update .env with your configuration"
        fi
        
        # Start development services
        docker-compose down
        docker-compose up -d
        
        echo "✅ Development environment started!"
        echo "🌐 API: http://localhost:8080"
        echo "🗄️  Database: localhost:5432"
        echo "🌍 Nginx: http://localhost"
        ;;
        
    "production"|"prod")
        echo "📋 Starting production environment..."
        
        # Check if production env exists
        if [ ! -f .env.production ]; then
            echo "❌ .env.production not found!"
            echo "📝 Please copy and configure deploy/.env.production.template to .env.production"
            exit 1
        fi
        
        # Build the application
        echo "🏗️  Building application..."
        docker build -t rockerstt-backend:latest .
        
        # Start production services
        docker-compose -f deploy/docker-compose.prod.yml down
        docker-compose -f deploy/docker-compose.prod.yml --env-file .env.production up -d
        
        echo "✅ Production environment started!"
        echo "🌐 API: https://localhost"
        ;;
        
    "test")
        echo "📋 Running tests..."
        
        # Build test image
        docker build -t rockerstt-backend:test .
        
        # Run tests in container
        docker run --rm \
            -e DATABASE_URL="postgresql://postgres:postgres@localhost:5432/rockerstt_test?sslmode=disable" \
            -e HMAC_SECRET="test-secret-key-for-testing-only" \
            rockerstt-backend:test \
            go test -v ./...
        
        echo "✅ Tests completed!"
        ;;
        
    "stop")
        echo "🛑 Stopping all services..."
        docker-compose down
        docker-compose -f deploy/docker-compose.prod.yml down
        echo "✅ All services stopped!"
        ;;
        
    "clean")
        echo "🧹 Cleaning up..."
        docker-compose down -v
        docker-compose -f deploy/docker-compose.prod.yml down -v
        docker system prune -f
        echo "✅ Cleanup completed!"
        ;;
        
    *)
        echo "❌ Unknown environment: $ENVIRONMENT"
        echo ""
        echo "Usage: $0 [environment]"
        echo ""
        echo "Environments:"
        echo "  development, dev  - Start development environment"
        echo "  production, prod  - Start production environment"
        echo "  test             - Run tests"
        echo "  stop             - Stop all services"
        echo "  clean            - Clean up containers and volumes"
        exit 1
        ;;
esac

echo ""
echo "📝 Useful commands:"
echo "  View logs: docker-compose logs -f"
echo "  Check status: docker-compose ps"
echo "  Stop services: $0 stop"