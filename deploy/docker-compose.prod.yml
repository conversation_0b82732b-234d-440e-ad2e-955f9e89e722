services:
  # RockerSTT Backend API (Production)
  api:
    image: rockerstt-backend:latest
    container_name: rockerstt-api-prod
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - HMAC_SECRET=${HMAC_SECRET}
      - TOKEN_EXPIRY_MINS=${TOKEN_EXPIRY_MINS:-5}
      - APPLE_JWK_URL=${APPLE_JWK_URL:-https://appleid.apple.com/auth/keys}
      - FUNASR_URL=${FUNASR_URL}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - PORT=8080
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: rockerstt-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ${SSL_CERT_PATH:-./ssl}:/etc/nginx/ssl:ro
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.25'
        reservations:
          memory: 64M
          cpus: '0.1'

networks:
  default:
    name: rockerstt-prod-network