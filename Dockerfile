# Multi-stage Dockerfile for RockerSTT Backend
# Stage 1: Build stage
FROM golang:1.23-alpine AS builder

# Install git and ca-certificates (needed for fetching dependencies and HTTPS)
RUN apk add --no-cache git ca-certificates tzdata

# Create a non-root user for building
RUN adduser -D -g '' appuser

# Set working directory
WORKDIR /build

# Copy go mod files first for better caching
COPY go.mod go.sum ./

# Download dependencies with verification
RUN go mod download && go mod verify

# Copy source code
COPY . .

# Build the application with optimizations
# CGO_ENABLED=0 for static binary, GOOS=linux for Linux target
# -ldflags for smaller binary size and build info
RUN CGO_ENABLED=0 GOOS=linux go build \
    -a -installsuffix cgo \
    -ldflags '-w -s -extldflags "-static"' \
    -o server ./cmd/server

# Verify the binary was built correctly
RUN ./server --help || echo "Binary built successfully"

# Stage 2: Runtime stage
FROM alpine:latest

# Install ca-certificates for HTTPS requests to Apple JWK endpoint
# Also install wget for health checks
RUN apk --no-cache add ca-certificates tzdata wget && \
    rm -rf /var/cache/apk/*

# Create a non-root user with specific UID/GID for consistency
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Set working directory
WORKDIR /app

# Copy the binary from builder stage with proper ownership
COPY --from=builder --chown=appuser:appgroup /build/server .

# Copy timezone data
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo

# Ensure binary is executable
RUN chmod +x ./server

# Switch to non-root user
USER appuser

# Expose port (configurable via environment variable)
EXPOSE 8080

# Environment variables with defaults
ENV PORT=8080
ENV LOG_LEVEL=info
ENV TOKEN_EXPIRY_MINS=5
ENV HMAC_SECRET=development-secret-key-change-in-production-minimum-32-chars
ENV APPLE_BUNDLE_ID=com.example.rockerstt.dev
ENV APPLE_JWK_URL=https://appleid.apple.com/auth/keys
ENV FUNASR_URL=ws://host.docker.internal:10096

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:${PORT}/health || exit 1

# Run the application
CMD ["./server"]