# Docker Environment Configuration
# Copy this file to .env and update the values for your environment

# Server Configuration
PORT=8080

# Database Configuration (for Docker Compose)
DATABASE_URL=**************************************/rockerstt?sslmode=disable

# Security Configuration
HMAC_SECRET=your-secure-hmac-secret-key-here-minimum-32-characters-for-production

# Token Configuration
TOKEN_EXPIRY_MINS=5

# Apple Sign-In Configuration
APPLE_BUNDLE_ID=your.app.bundle.id
APPLE_JWK_URL=https://appleid.apple.com/auth/keys

# FunASR Configuration (external FunASR server on host machine)
FUNASR_URL=ws://host.docker.internal:10096

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json