package main

import (
	"fmt"
	"log"
	"os"

	"rockerstt-backend/internal/auth"
	"rockerstt-backend/internal/config"
	"rockerstt-backend/internal/database"
	"rockerstt-backend/internal/logger"
	"rockerstt-backend/internal/server"
	"rockerstt-backend/internal/token"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Validate required configuration
	if cfg.Token.HMACSecret == "" {
		log.Fatal("HMAC_SECRET environment variable is required")
	}
	if cfg.Apple.BundleID == "" {
		log.Fatal("APPLE_BUNDLE_ID environment variable is required")
	}

	// Initialize logger
	appLogger := logger.New(cfg)

	// Initialize database
	var databaseURL string
	if envDatabaseURL := os.Getenv("DATABASE_URL"); envDatabaseURL != "" {
		// Use DATABASE_URL if provided (for Docker environments)
		databaseURL = envDatabaseURL
	} else {
		// Construct from individual environment variables
		databaseURL = fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
			cfg.Database.Host,
			cfg.Database.Port,
			cfg.Database.User,
			cfg.Database.Password,
			cfg.Database.DBName,
			cfg.Database.SSLMode,
		)
	}

	db, err := database.New(databaseURL)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Initialize database schema
	if err := db.Initialize(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Initialize JWK service for Apple token validation
	jwkService := auth.NewJWKService(cfg.Apple.JWKUrl, appLogger.Logger)

	// Initialize Apple token validator
	appleValidator := auth.NewAppleTokenValidator(jwkService, cfg.Apple.BundleID, appLogger.Logger)

	// Initialize auth service
	authSvc := auth.NewAuthService(appleValidator, db, appLogger.Logger)

	// Initialize token service
	tokenSvc := token.NewTokenService(cfg.Token.HMACSecret, cfg.Token.ExpiryMinutes)

	// Create and start server
	srv := server.New(cfg, authSvc, tokenSvc, db)
	if err := srv.Start(); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}