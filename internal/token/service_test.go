package token

import (
	"encoding/base64"
	"encoding/json"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewTokenService(t *testing.T) {
	secret := "test-secret"
	expiryMinutes := 5

	service := NewTokenService(secret, expiryMinutes)

	assert.NotNil(t, service)
	assert.Equal(t, []byte(secret), service.secret)
	assert.Equal(t, expiryMinutes, service.expiryMinutes)
}

func TestGenerateToken(t *testing.T) {
	service := NewTokenService("test-secret", 5)

	tests := []struct {
		name    string
		userID  string
		wantErr bool
	}{
		{
			name:    "valid user ID",
			userID:  "user123",
			wantErr: false,
		},
		{
			name:    "empty user ID",
			userID:  "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token, err := service.GenerateToken(tt.userID)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, token)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, token)

				// Token should have two parts separated by a dot
				parts := strings.Split(token, ".")
				assert.Len(t, parts, 2)

				// First part should be base64 encoded payload
				payloadBytes, err := base64.URLEncoding.DecodeString(parts[0])
				require.NoError(t, err)

				var payload TokenPayload
				err = json.Unmarshal(payloadBytes, &payload)
				require.NoError(t, err)

				assert.Equal(t, tt.userID, payload.UserID)
				assert.NotEmpty(t, payload.SessionID)
				assert.Greater(t, payload.Timestamp, int64(0))

				// Second part should be base64 encoded signature
				_, err = base64.URLEncoding.DecodeString(parts[1])
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateToken(t *testing.T) {
	service := NewTokenService("test-secret", 5)
	userID := "user123"

	// Generate a valid token
	validToken, err := service.GenerateToken(userID)
	require.NoError(t, err)

	tests := []struct {
		name    string
		token   string
		wantErr bool
		setup   func() string
	}{
		{
			name:    "valid token",
			token:   validToken,
			wantErr: false,
		},
		{
			name:    "empty token",
			token:   "",
			wantErr: true,
		},
		{
			name:    "invalid format - no dot",
			token:   "invalidtoken",
			wantErr: true,
		},
		{
			name:    "invalid format - too many parts",
			token:   "part1.part2.part3",
			wantErr: true,
		},
		{
			name:    "invalid signature",
			wantErr: true,
			setup: func() string {
				parts := strings.Split(validToken, ".")
				return parts[0] + ".invalidsignature"
			},
		},
		{
			name:    "invalid base64 payload",
			token:   "invalid-base64.signature",
			wantErr: true,
		},
		{
			name:    "expired token",
			wantErr: true,
			setup: func() string {
				// Create a token with past timestamp manually
				pastTime := time.Now().Add(-10 * time.Minute).Unix()
				payload := TokenPayload{
					UserID:    userID,
					Timestamp: pastTime,
					SessionID: "session123",
				}
				payloadBytes, _ := json.Marshal(payload)
				payloadB64 := base64.URLEncoding.EncodeToString(payloadBytes)
				signature := service.generateSignature(payloadB64)
				return payloadB64 + "." + signature
			},
		},
		{
			name:    "future timestamp token",
			wantErr: true,
			setup: func() string {
				// Create a token with future timestamp
				futureTime := time.Now().Add(1 * time.Hour).Unix()
				payload := TokenPayload{
					UserID:    userID,
					Timestamp: futureTime,
					SessionID: "session123",
				}
				payloadBytes, _ := json.Marshal(payload)
				payloadB64 := base64.URLEncoding.EncodeToString(payloadBytes)
				signature := service.generateSignature(payloadB64)
				return payloadB64 + "." + signature
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token := tt.token
			if tt.setup != nil {
				token = tt.setup()
			}

			claims, err := service.ValidateToken(token)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, claims)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, claims)
				assert.Equal(t, userID, claims.UserID)
				assert.True(t, claims.Valid)
				assert.NotEmpty(t, claims.SessionID)
				assert.Greater(t, claims.Timestamp, int64(0))
			}
		})
	}
}

func TestValidateToken_DifferentSecrets(t *testing.T) {
	service1 := NewTokenService("secret1", 5)
	service2 := NewTokenService("secret2", 5)

	// Generate token with service1
	token, err := service1.GenerateToken("user123")
	require.NoError(t, err)

	// Try to validate with service2 (different secret)
	claims, err := service2.ValidateToken(token)
	assert.Error(t, err)
	assert.Nil(t, claims)
	assert.Contains(t, err.Error(), "Invalid token signature")
}

func TestIsTokenExpired(t *testing.T) {
	service := NewTokenService("test-secret", 5)

	// Valid token
	validToken, err := service.GenerateToken("user123")
	require.NoError(t, err)
	assert.False(t, service.IsTokenExpired(validToken))

	// Invalid token
	assert.True(t, service.IsTokenExpired("invalid-token"))

	// Empty token
	assert.True(t, service.IsTokenExpired(""))
}

func TestExtractSessionID(t *testing.T) {
	service := NewTokenService("test-secret", 5)

	// Generate a valid token
	token, err := service.GenerateToken("user123")
	require.NoError(t, err)

	tests := []struct {
		name    string
		token   string
		wantErr bool
	}{
		{
			name:    "valid token",
			token:   token,
			wantErr: false,
		},
		{
			name:    "empty token",
			token:   "",
			wantErr: true,
		},
		{
			name:    "invalid format",
			token:   "invalid-token",
			wantErr: true,
		},
		{
			name:    "invalid base64",
			token:   "invalid-base64.signature",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sessionID, err := service.ExtractSessionID(tt.token)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, sessionID)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, sessionID)

				// Verify it matches the session ID from full validation
				claims, err := service.ValidateToken(tt.token)
				require.NoError(t, err)
				assert.Equal(t, claims.SessionID, sessionID)
			}
		})
	}
}

func TestGenerateSignature(t *testing.T) {
	service := NewTokenService("test-secret", 5)

	payload := "test-payload"
	signature1 := service.generateSignature(payload)
	signature2 := service.generateSignature(payload)

	// Same payload should generate same signature
	assert.Equal(t, signature1, signature2)

	// Different payload should generate different signature
	signature3 := service.generateSignature("different-payload")
	assert.NotEqual(t, signature1, signature3)

	// Signature should be base64 encoded
	_, err := base64.URLEncoding.DecodeString(signature1)
	assert.NoError(t, err)
}

func TestTokenStructure(t *testing.T) {
	service := NewTokenService("test-secret", 5)
	userID := "user123"

	token, err := service.GenerateToken(userID)
	require.NoError(t, err)

	// Validate token structure
	parts := strings.Split(token, ".")
	require.Len(t, parts, 2)

	// Decode and verify payload structure
	payloadBytes, err := base64.URLEncoding.DecodeString(parts[0])
	require.NoError(t, err)

	var payload TokenPayload
	err = json.Unmarshal(payloadBytes, &payload)
	require.NoError(t, err)

	// Verify all required fields are present
	assert.Equal(t, userID, payload.UserID)
	assert.NotEmpty(t, payload.SessionID)
	assert.Greater(t, payload.Timestamp, int64(0))

	// Verify timestamp is recent (within last minute)
	now := time.Now().Unix()
	assert.True(t, now-payload.Timestamp < 60, "Token timestamp should be recent")
}

func TestTokenExpiry(t *testing.T) {
	// Create service with 1-second expiry for testing
	service := NewTokenService("test-secret", 0) // 0 minutes = immediate expiry

	token, err := service.GenerateToken("user123")
	require.NoError(t, err)

	// Token should be valid immediately
	claims, err := service.ValidateToken(token)
	if err == nil {
		assert.True(t, claims.Valid)
	}

	// Wait and token should be expired
	time.Sleep(1 * time.Second)
	claims, err = service.ValidateToken(token)
	assert.Error(t, err)
	assert.Nil(t, claims)
	assert.Contains(t, err.Error(), "expired")
}

func TestConcurrentTokenGeneration(t *testing.T) {
	service := NewTokenService("test-secret", 5)
	userID := "user123"

	// Generate multiple tokens concurrently
	tokens := make(chan string, 10)
	for i := 0; i < 10; i++ {
		go func() {
			token, err := service.GenerateToken(userID)
			require.NoError(t, err)
			tokens <- token
		}()
	}

	// Collect all tokens
	var generatedTokens []string
	for i := 0; i < 10; i++ {
		generatedTokens = append(generatedTokens, <-tokens)
	}

	// All tokens should be unique (different session IDs)
	sessionIDs := make(map[string]bool)
	for _, token := range generatedTokens {
		sessionID, err := service.ExtractSessionID(token)
		require.NoError(t, err)
		assert.False(t, sessionIDs[sessionID], "Session ID should be unique")
		sessionIDs[sessionID] = true
	}

	// All tokens should be valid
	for _, token := range generatedTokens {
		claims, err := service.ValidateToken(token)
		assert.NoError(t, err)
		assert.Equal(t, userID, claims.UserID)
		assert.True(t, claims.Valid)
	}
}

// Additional comprehensive test cases for TokenService

func TestTokenService_ErrorHandling(t *testing.T) {
	service := NewTokenService("test-secret", 5)

	t.Run("GenerateToken_EmptyUserID", func(t *testing.T) {
		token, err := service.GenerateToken("")
		assert.Error(t, err)
		assert.Empty(t, token)
		assert.Contains(t, err.Error(), "User ID cannot be empty")
	})

	t.Run("ValidateToken_EmptyToken", func(t *testing.T) {
		claims, err := service.ValidateToken("")
		assert.Error(t, err)
		assert.Nil(t, claims)
		assert.Contains(t, err.Error(), "Token cannot be empty")
	})

	t.Run("ValidateToken_MalformedToken_NoDot", func(t *testing.T) {
		claims, err := service.ValidateToken("malformedtoken")
		assert.Error(t, err)
		assert.Nil(t, claims)
		assert.Contains(t, err.Error(), "Invalid token format")
	})

	t.Run("ValidateToken_MalformedToken_TooManyParts", func(t *testing.T) {
		claims, err := service.ValidateToken("part1.part2.part3")
		assert.Error(t, err)
		assert.Nil(t, claims)
		assert.Contains(t, err.Error(), "Invalid token format")
	})

	t.Run("ValidateToken_InvalidBase64Payload", func(t *testing.T) {
		claims, err := service.ValidateToken("invalid-base64!@#.signature")
		assert.Error(t, err)
		assert.Nil(t, claims)
		// The error could be either signature validation or base64 decoding
		assert.True(t, 
			strings.Contains(err.Error(), "Failed to decode token payload") ||
			strings.Contains(err.Error(), "Invalid token signature"),
		)
	})

	t.Run("ValidateToken_InvalidJSONPayload", func(t *testing.T) {
		// Create invalid JSON payload
		invalidJSON := base64.URLEncoding.EncodeToString([]byte("{invalid json"))
		signature := service.generateSignature(invalidJSON)
		token := invalidJSON + "." + signature
		
		claims, err := service.ValidateToken(token)
		assert.Error(t, err)
		assert.Nil(t, claims)
		assert.Contains(t, err.Error(), "Failed to parse token payload")
	})
}

func TestTokenService_EdgeCases(t *testing.T) {
	t.Run("ZeroExpiryTime", func(t *testing.T) {
		service := NewTokenService("test-secret", 0)

		token, err := service.GenerateToken("user123")
		require.NoError(t, err)

		// Token should be immediately expired or very close to expiry
		// Wait at least 1 second to ensure token is expired
		time.Sleep(1100 * time.Millisecond)
		claims, err := service.ValidateToken(token)
		assert.Error(t, err)
		assert.Nil(t, claims)
		if err != nil {
			assert.Contains(t, err.Error(), "expired")
		}
	})

	t.Run("VeryLongUserID", func(t *testing.T) {
		service := NewTokenService("test-secret", 5)
		longUserID := string(make([]byte, 1000)) // Very long user ID
		for i := range longUserID {
			longUserID = longUserID[:i] + "a" + longUserID[i+1:]
		}
		
		token, err := service.GenerateToken(longUserID)
		assert.NoError(t, err)
		assert.NotEmpty(t, token)
		
		claims, err := service.ValidateToken(token)
		assert.NoError(t, err)
		assert.Equal(t, longUserID, claims.UserID)
	})

	t.Run("SpecialCharactersInUserID", func(t *testing.T) {
		service := NewTokenService("test-secret", 5)
		specialUserID := "<EMAIL>/special-chars_123!@#$%^&*()"
		
		token, err := service.GenerateToken(specialUserID)
		assert.NoError(t, err)
		assert.NotEmpty(t, token)
		
		claims, err := service.ValidateToken(token)
		assert.NoError(t, err)
		assert.Equal(t, specialUserID, claims.UserID)
	})
}

func TestTokenService_SecurityTests(t *testing.T) {
	t.Run("TokenTampering_ModifiedPayload", func(t *testing.T) {
		service := NewTokenService("test-secret", 5)
		
		token, err := service.GenerateToken("user123")
		require.NoError(t, err)
		
		parts := strings.Split(token, ".")
		require.Len(t, parts, 2)
		
		// Decode, modify, and re-encode payload
		payloadBytes, err := base64.URLEncoding.DecodeString(parts[0])
		require.NoError(t, err)
		
		var payload TokenPayload
		err = json.Unmarshal(payloadBytes, &payload)
		require.NoError(t, err)
		
		// Modify user ID
		payload.UserID = "hacker123"
		modifiedPayloadBytes, err := json.Marshal(payload)
		require.NoError(t, err)
		
		modifiedPayloadB64 := base64.URLEncoding.EncodeToString(modifiedPayloadBytes)
		tamperedToken := modifiedPayloadB64 + "." + parts[1]
		
		// Should fail validation due to signature mismatch
		claims, err := service.ValidateToken(tamperedToken)
		assert.Error(t, err)
		assert.Nil(t, claims)
		assert.Contains(t, err.Error(), "Invalid token signature")
	})

	t.Run("TokenTampering_ModifiedSignature", func(t *testing.T) {
		service := NewTokenService("test-secret", 5)
		
		token, err := service.GenerateToken("user123")
		require.NoError(t, err)
		
		parts := strings.Split(token, ".")
		require.Len(t, parts, 2)
		
		// Modify signature
		tamperedToken := parts[0] + ".tampered-signature"
		
		claims, err := service.ValidateToken(tamperedToken)
		assert.Error(t, err)
		assert.Nil(t, claims)
		assert.Contains(t, err.Error(), "Invalid token signature")
	})

	t.Run("CrossSecretValidation", func(t *testing.T) {
		service1 := NewTokenService("secret1", 5)
		service2 := NewTokenService("secret2", 5)
		
		token, err := service1.GenerateToken("user123")
		require.NoError(t, err)
		
		// Should fail when validated with different secret
		claims, err := service2.ValidateToken(token)
		assert.Error(t, err)
		assert.Nil(t, claims)
		assert.Contains(t, err.Error(), "Invalid token signature")
	})
}

func TestTokenService_TimestampValidation(t *testing.T) {
	service := NewTokenService("test-secret", 5)

	t.Run("FutureTimestamp", func(t *testing.T) {
		// Create token with future timestamp
		futureTime := time.Now().Add(1 * time.Hour).Unix()
		payload := TokenPayload{
			UserID:    "user123",
			Timestamp: futureTime,
			SessionID: "session123",
		}
		payloadBytes, _ := json.Marshal(payload)
		payloadB64 := base64.URLEncoding.EncodeToString(payloadBytes)
		signature := service.generateSignature(payloadB64)
		token := payloadB64 + "." + signature
		
		claims, err := service.ValidateToken(token)
		assert.Error(t, err)
		assert.Nil(t, claims)
		assert.Contains(t, err.Error(), "Token timestamp is in the future")
	})

	t.Run("VeryOldTimestamp", func(t *testing.T) {
		// Create token with very old timestamp
		oldTime := time.Now().Add(-1 * time.Hour).Unix()
		payload := TokenPayload{
			UserID:    "user123",
			Timestamp: oldTime,
			SessionID: "session123",
		}
		payloadBytes, _ := json.Marshal(payload)
		payloadB64 := base64.URLEncoding.EncodeToString(payloadBytes)
		signature := service.generateSignature(payloadB64)
		token := payloadB64 + "." + signature
		
		claims, err := service.ValidateToken(token)
		assert.Error(t, err)
		assert.Nil(t, claims)
		assert.Contains(t, err.Error(), "Token has expired")
	})

	t.Run("BoundaryTimestamp", func(t *testing.T) {
		// Create token that's exactly at the expiry boundary
		boundaryTime := time.Now().Add(-5*time.Minute + 1*time.Second).Unix()
		payload := TokenPayload{
			UserID:    "user123",
			Timestamp: boundaryTime,
			SessionID: "session123",
		}
		payloadBytes, _ := json.Marshal(payload)
		payloadB64 := base64.URLEncoding.EncodeToString(payloadBytes)
		signature := service.generateSignature(payloadB64)
		token := payloadB64 + "." + signature
		
		claims, err := service.ValidateToken(token)
		assert.NoError(t, err)
		assert.NotNil(t, claims)
		assert.True(t, claims.Valid)
	})
}

func TestTokenService_ExtractSessionID_EdgeCases(t *testing.T) {
	service := NewTokenService("test-secret", 5)

	t.Run("ExtractFromValidToken", func(t *testing.T) {
		token, err := service.GenerateToken("user123")
		require.NoError(t, err)
		
		sessionID, err := service.ExtractSessionID(token)
		assert.NoError(t, err)
		assert.NotEmpty(t, sessionID)
		
		// Should match the session ID from full validation
		claims, err := service.ValidateToken(token)
		require.NoError(t, err)
		assert.Equal(t, claims.SessionID, sessionID)
	})

	t.Run("ExtractFromExpiredToken", func(t *testing.T) {
		// Create expired token
		oldTime := time.Now().Add(-1 * time.Hour).Unix()
		payload := TokenPayload{
			UserID:    "user123",
			Timestamp: oldTime,
			SessionID: "expired-session-123",
		}
		payloadBytes, _ := json.Marshal(payload)
		payloadB64 := base64.URLEncoding.EncodeToString(payloadBytes)
		signature := service.generateSignature(payloadB64)
		token := payloadB64 + "." + signature
		
		// Should still extract session ID even if token is expired
		sessionID, err := service.ExtractSessionID(token)
		assert.NoError(t, err)
		assert.Equal(t, "expired-session-123", sessionID)
		
		// But full validation should fail
		claims, err := service.ValidateToken(token)
		assert.Error(t, err)
		assert.Nil(t, claims)
	})

	t.Run("ExtractFromMalformedToken", func(t *testing.T) {
		sessionID, err := service.ExtractSessionID("malformed-token")
		assert.Error(t, err)
		assert.Empty(t, sessionID)
		assert.Contains(t, err.Error(), "Invalid token format")
	})
}