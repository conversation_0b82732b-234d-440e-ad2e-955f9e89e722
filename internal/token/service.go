package token

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"strings"
	"time"

	"github.com/google/uuid"
	"rockerstt-backend/internal/errors"
)

// TokenService handles HMAC token generation and validation
type TokenService struct {
	secret       []byte
	expiryMinutes int
}

// TokenClaims represents the claims in an HMAC token
type TokenClaims struct {
	UserID    string `json:"user_id"`
	Timestamp int64  `json:"timestamp"`
	SessionID string `json:"session_id"`
	Valid     bool   `json:"-"`
}

// TokenPayload represents the structure of the token before signing
type TokenPayload struct {
	UserID    string `json:"user_id"`
	Timestamp int64  `json:"timestamp"`
	SessionID string `json:"session_id"`
}

// NewTokenService creates a new token service with the given secret and expiry
func NewTokenService(secret string, expiryMinutes int) *TokenService {
	return &TokenService{
		secret:       []byte(secret),
		expiryMinutes: expiryMinutes,
	}
}

// GenerateToken generates an HMAC-signed token for the given user ID
func (ts *TokenService) GenerateToken(userID string) (string, error) {
	if userID == "" {
		return "", errors.NewValidationError(
			errors.CodeMissingRequiredField,
			"User ID cannot be empty",
			nil,
		)
	}

	// Generate unique session ID
	sessionID := uuid.New().String()
	
	// Create timestamp
	timestamp := time.Now().Unix()

	// Create token payload
	payload := TokenPayload{
		UserID:    userID,
		Timestamp: timestamp,
		SessionID: sessionID,
	}

	// Marshal payload to JSON
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return "", errors.NewServerError(
			errors.CodeInternalError,
			"Failed to create token payload",
			err,
		)
	}

	// Encode payload as base64
	payloadB64 := base64.URLEncoding.EncodeToString(payloadBytes)

	// Generate HMAC signature
	signature := ts.generateSignature(payloadB64)

	// Combine payload and signature
	token := payloadB64 + "." + signature

	return token, nil
}

// generateSignature generates HMAC-SHA256 signature for the given payload
func (ts *TokenService) generateSignature(payload string) string {
	h := hmac.New(sha256.New, ts.secret)
	h.Write([]byte(payload))
	signature := h.Sum(nil)
	return base64.URLEncoding.EncodeToString(signature)
}

// ValidateToken validates an HMAC token and returns the claims
func (ts *TokenService) ValidateToken(token string) (*TokenClaims, error) {
	if token == "" {
		return nil, errors.NewValidationError(
			errors.CodeMissingToken,
			"Token cannot be empty",
			nil,
		)
	}

	// Split token into payload and signature
	parts := strings.Split(token, ".")
	if len(parts) != 2 {
		return nil, errors.NewAuthError(
			errors.CodeTokenMalformed,
			"Invalid token format",
			nil,
		)
	}

	payloadB64 := parts[0]
	providedSignature := parts[1]

	// Verify signature
	expectedSignature := ts.generateSignature(payloadB64)
	if !hmac.Equal([]byte(providedSignature), []byte(expectedSignature)) {
		return nil, errors.NewAuthError(
			errors.CodeTokenMalformed,
			"Invalid token signature",
			nil,
		)
	}

	// Decode payload
	payloadBytes, err := base64.URLEncoding.DecodeString(payloadB64)
	if err != nil {
		return nil, errors.NewAuthError(
			errors.CodeTokenMalformed,
			"Failed to decode token payload",
			err,
		)
	}

	// Unmarshal payload
	var payload TokenPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		return nil, errors.NewAuthError(
			errors.CodeTokenMalformed,
			"Failed to parse token payload",
			err,
		)
	}

	// Validate timestamp (check if token is within expiry window)
	now := time.Now().Unix()
	tokenAge := now - payload.Timestamp
	maxAge := int64(ts.expiryMinutes * 60) // Convert minutes to seconds

	if tokenAge > maxAge {
		return nil, errors.NewAuthError(
			errors.CodeTokenExpired,
			"Token has expired",
			nil,
		)
	}

	if tokenAge < 0 {
		return nil, errors.NewAuthError(
			errors.CodeTokenMalformed,
			"Token timestamp is in the future",
			nil,
		)
	}

	// Return valid claims
	claims := &TokenClaims{
		UserID:    payload.UserID,
		Timestamp: payload.Timestamp,
		SessionID: payload.SessionID,
		Valid:     true,
	}

	return claims, nil
}

// IsTokenExpired checks if a token is expired without full validation
func (ts *TokenService) IsTokenExpired(token string) bool {
	claims, err := ts.ValidateToken(token)
	if err != nil {
		return true
	}
	return !claims.Valid
}

// ExtractSessionID extracts session ID from token without full validation
func (ts *TokenService) ExtractSessionID(token string) (string, error) {
	if token == "" {
		return "", errors.NewValidationError(
			errors.CodeMissingToken,
			"Token cannot be empty",
			nil,
		)
	}

	parts := strings.Split(token, ".")
	if len(parts) != 2 {
		return "", errors.NewAuthError(
			errors.CodeTokenMalformed,
			"Invalid token format",
			nil,
		)
	}

	payloadBytes, err := base64.URLEncoding.DecodeString(parts[0])
	if err != nil {
		return "", errors.NewAuthError(
			errors.CodeTokenMalformed,
			"Failed to decode token payload",
			err,
		)
	}

	var payload TokenPayload
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		return "", errors.NewAuthError(
			errors.CodeTokenMalformed,
			"Failed to parse token payload",
			err,
		)
	}

	return payload.SessionID, nil
}