package database

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"rockerstt-backend/internal/models"
)

// Service provides high-level database operations with business logic
type Service struct {
	db     Database
	logger *logrus.Logger
}

// NewService creates a new database service
func NewService(db Database, logger *logrus.Logger) *Service {
	return &Service{
		db:     db,
		logger: logger,
	}
}

// GetOrCreateUser retrieves an existing user or creates a new one
func (s *Service) GetOrCreateUser(appleUserID, email string) (*models.User, error) {
	start := time.Now()
	
	s.logger.WithFields(logrus.Fields{
		"apple_user_id": appleUserID,
		"email":         email,
		"operation":     "get_or_create_user",
	}).Debug("Starting user lookup")

	// Try to get existing user
	user, err := s.db.GetUser(appleUserID)
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"apple_user_id": appleUserID,
			"operation":     "get_user",
			"duration_ms":   float64(time.Since(start).Nanoseconds()) / 1e6,
		}).Error("Failed to check existing user")
		return nil, fmt.Errorf("failed to check existing user: %w", err)
	}

	// If user exists, return it
	if user != nil {
		s.logger.WithFields(logrus.Fields{
			"user_id":       user.ID,
			"apple_user_id": user.AppleUserID,
			"operation":     "get_user",
			"duration_ms":   float64(time.Since(start).Nanoseconds()) / 1e6,
		}).Debug("Existing user found")
		return user, nil
	}

	// Create new user
	createStart := time.Now()
	newUser := &models.User{
		AppleUserID: appleUserID,
		Email:       email,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.db.CreateUser(newUser); err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"apple_user_id": appleUserID,
			"email":         email,
			"operation":     "create_user",
			"duration_ms":   float64(time.Since(createStart).Nanoseconds()) / 1e6,
		}).Error("Failed to create new user")
		return nil, fmt.Errorf("failed to create new user: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":       newUser.ID,
		"apple_user_id": newUser.AppleUserID,
		"email":         newUser.Email,
		"operation":     "create_user",
		"duration_ms":   float64(time.Since(createStart).Nanoseconds()) / 1e6,
		"total_duration_ms": float64(time.Since(start).Nanoseconds()) / 1e6,
	}).Info("New user created successfully")

	return newUser, nil
}

// StartSession creates a new session record
func (s *Service) StartSession(userID string) (string, error) {
	start := time.Now()
	sessionID := uuid.New().String()
	
	s.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"session_id": sessionID,
		"operation":  "start_session",
	}).Info("Starting new session")
	
	session := &models.Session{
		SessionID: sessionID,
		UserID:    userID,
		StartTime: time.Now(),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.db.CreateSession(session); err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":     userID,
			"session_id":  sessionID,
			"operation":   "create_session",
			"duration_ms": float64(time.Since(start).Nanoseconds()) / 1e6,
		}).Error("Failed to start session")
		return "", fmt.Errorf("failed to start session: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":     userID,
		"session_id":  sessionID,
		"operation":   "create_session",
		"duration_ms": float64(time.Since(start).Nanoseconds()) / 1e6,
	}).Info("Session started successfully")

	return sessionID, nil
}

// EndSession updates a session with end time and calculates duration
func (s *Service) EndSession(sessionID string) error {
	start := time.Now()
	
	s.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"operation":  "end_session",
	}).Info("Ending session")

	// Get the session to calculate duration
	session, err := s.db.GetSession(sessionID)
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"session_id":  sessionID,
			"operation":   "get_session",
			"duration_ms": float64(time.Since(start).Nanoseconds()) / 1e6,
		}).Error("Failed to get session for ending")
		return fmt.Errorf("failed to get session for ending: %w", err)
	}

	if session == nil {
		s.logger.WithFields(logrus.Fields{
			"session_id":  sessionID,
			"operation":   "get_session",
			"duration_ms": float64(time.Since(start).Nanoseconds()) / 1e6,
		}).Warn("Session not found for ending")
		return fmt.Errorf("session %s not found", sessionID)
	}

	endTime := time.Now()
	sessionDuration := int(endTime.Sub(session.StartTime).Seconds())

	updateStart := time.Now()
	if err := s.db.UpdateSession(sessionID, endTime, sessionDuration); err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"session_id":       sessionID,
			"user_id":          session.UserID,
			"session_duration": sessionDuration,
			"operation":        "update_session",
			"duration_ms":      float64(time.Since(updateStart).Nanoseconds()) / 1e6,
		}).Error("Failed to end session")
		return fmt.Errorf("failed to end session: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"session_id":       sessionID,
		"user_id":          session.UserID,
		"session_duration": sessionDuration,
		"operation":        "update_session",
		"duration_ms":      float64(time.Since(updateStart).Nanoseconds()) / 1e6,
		"total_duration_ms": float64(time.Since(start).Nanoseconds()) / 1e6,
	}).Info("Session ended successfully")

	return nil
}

// GetUserSessions retrieves all sessions for a user with pagination
func (s *Service) GetUserSessions(userID string, limit, offset int) ([]models.Session, error) {
	start := time.Now()
	
	s.logger.WithFields(logrus.Fields{
		"user_id":   userID,
		"limit":     limit,
		"offset":    offset,
		"operation": "get_user_sessions",
	}).Debug("Retrieving user sessions")

	sessions, err := s.db.GetUserSessions(userID)
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":     userID,
			"operation":   "get_user_sessions",
			"duration_ms": float64(time.Since(start).Nanoseconds()) / 1e6,
		}).Error("Failed to retrieve user sessions")
		return nil, err
	}

	totalSessions := len(sessions)

	// Apply pagination if specified
	if limit > 0 {
		startIdx := offset
		if startIdx > len(sessions) {
			s.logger.WithFields(logrus.Fields{
				"user_id":       userID,
				"total_sessions": totalSessions,
				"offset":        offset,
				"operation":     "get_user_sessions",
				"duration_ms":   float64(time.Since(start).Nanoseconds()) / 1e6,
			}).Debug("Offset beyond available sessions, returning empty result")
			return []models.Session{}, nil
		}

		endIdx := startIdx + limit
		if endIdx > len(sessions) {
			endIdx = len(sessions)
		}

		sessions = sessions[startIdx:endIdx]
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":         userID,
		"total_sessions":  totalSessions,
		"returned_sessions": len(sessions),
		"limit":           limit,
		"offset":          offset,
		"operation":       "get_user_sessions",
		"duration_ms":     float64(time.Since(start).Nanoseconds()) / 1e6,
	}).Debug("User sessions retrieved successfully")

	return sessions, nil
}

// GetUserCount returns the total number of users
func (s *Service) GetUserCount() (int64, error) {
	start := time.Now()

	count, err := s.db.GetUserCount()
	duration := time.Since(start)

	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"operation":   "get_user_count",
			"duration_ms": float64(duration.Nanoseconds()) / 1e6,
		}).Error("Failed to get user count")
		return 0, err
	}

	s.logger.WithFields(logrus.Fields{
		"count":       count,
		"operation":   "get_user_count",
		"duration_ms": float64(duration.Nanoseconds()) / 1e6,
	}).Debug("User count retrieved successfully")

	return count, nil
}

// GetSessionCount returns the total number of sessions
func (s *Service) GetSessionCount() (int64, error) {
	start := time.Now()

	count, err := s.db.GetSessionCount()
	duration := time.Since(start)

	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"operation":   "get_session_count",
			"duration_ms": float64(duration.Nanoseconds()) / 1e6,
		}).Error("Failed to get session count")
		return 0, err
	}

	s.logger.WithFields(logrus.Fields{
		"count":       count,
		"operation":   "get_session_count",
		"duration_ms": float64(duration.Nanoseconds()) / 1e6,
	}).Debug("Session count retrieved successfully")

	return count, nil
}

// GetActiveSessionCount returns the number of active sessions
func (s *Service) GetActiveSessionCount() (int, error) {
	start := time.Now()

	count, err := s.db.GetActiveSessionCount()
	duration := time.Since(start)

	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"operation":   "get_active_session_count",
			"duration_ms": float64(duration.Nanoseconds()) / 1e6,
		}).Error("Failed to get active session count")
		return 0, err
	}

	s.logger.WithFields(logrus.Fields{
		"count":       count,
		"operation":   "get_active_session_count",
		"duration_ms": float64(duration.Nanoseconds()) / 1e6,
	}).Debug("Active session count retrieved successfully")

	return count, nil
}

// HealthCheck performs a comprehensive health check
func (s *Service) HealthCheck() error {
	start := time.Now()

	s.logger.WithField("operation", "health_check").Debug("Performing database health check")

	err := s.db.Ping()
	duration := time.Since(start)

	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"operation":   "health_check",
			"duration_ms": float64(duration.Nanoseconds()) / 1e6,
		}).Error("Database health check failed")
		return err
	}

	s.logger.WithFields(logrus.Fields{
		"operation":   "health_check",
		"duration_ms": float64(duration.Nanoseconds()) / 1e6,
	}).Debug("Database health check successful")

	return nil
}