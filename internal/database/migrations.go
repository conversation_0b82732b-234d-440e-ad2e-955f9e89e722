package database

import (
	"fmt"
	"log"
	"time"

	"rockerstt-backend/internal/models"
)

// MigrationService handles database migrations and seeding
type MigrationService struct {
	db *DB
}

// NewMigrationService creates a new migration service
func NewMigrationService(db *DB) *MigrationService {
	return &MigrationService{db: db}
}

// RunMigrations executes all database migrations
func (m *MigrationService) RunMigrations() error {
	log.Println("Starting database migrations...")

	// Run GORM auto-migration
	if err := m.autoMigrate(); err != nil {
		return fmt.Errorf("auto-migration failed: %w", err)
	}

	// Create custom indexes
	if err := m.createIndexes(); err != nil {
		return fmt.Errorf("index creation failed: %w", err)
	}

	// Add constraints
	if err := m.addConstraints(); err != nil {
		return fmt.Errorf("constraint creation failed: %w", err)
	}

	log.Println("Database migrations completed successfully")
	return nil
}

// autoMigrate runs GORM auto-migration for all models
func (m *MigrationService) autoMigrate() error {
	log.Println("Running GORM auto-migration...")
	
	err := m.db.DB.AutoMigrate(
		&models.User{},
		&models.Session{},
	)
	if err != nil {
		return fmt.Errorf("failed to migrate models: %w", err)
	}

	log.Println("GORM auto-migration completed")
	return nil
}

// createIndexes creates performance indexes
func (m *MigrationService) createIndexes() error {
	log.Println("Creating performance indexes...")

	indexes := []struct {
		name  string
		query string
	}{
		{
			name:  "sessions user_id index",
			query: "CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id)",
		},
		{
			name:  "sessions start_time index",
			query: "CREATE INDEX IF NOT EXISTS idx_sessions_start_time ON sessions(start_time DESC)",
		},
		{
			name:  "users apple_user_id unique index",
			query: "CREATE UNIQUE INDEX IF NOT EXISTS idx_users_apple_user_id_unique ON users(apple_user_id)",
		},
		{
			name:  "users email index",
			query: "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email) WHERE email IS NOT NULL",
		},
		{
			name:  "sessions composite index for user queries",
			query: "CREATE INDEX IF NOT EXISTS idx_sessions_user_start ON sessions(user_id, start_time DESC)",
		},
		{
			name:  "sessions duration index for analytics",
			query: "CREATE INDEX IF NOT EXISTS idx_sessions_duration ON sessions(duration_secs) WHERE duration_secs IS NOT NULL",
		},
	}

	for _, idx := range indexes {
		if err := m.db.Exec(idx.query).Error; err != nil {
			return fmt.Errorf("failed to create %s: %w", idx.name, err)
		}
		log.Printf("✓ Created %s", idx.name)
	}

	return nil
}

// addConstraints adds database constraints for data integrity
func (m *MigrationService) addConstraints() error {
	log.Println("Adding database constraints...")

	constraints := []struct {
		name  string
		query string
	}{
		{
			name:  "sessions duration check",
			query: "ALTER TABLE sessions ADD CONSTRAINT IF NOT EXISTS chk_sessions_duration CHECK (duration_secs >= 0)",
		},
		{
			name:  "sessions end_time check",
			query: "ALTER TABLE sessions ADD CONSTRAINT IF NOT EXISTS chk_sessions_end_time CHECK (end_time IS NULL OR end_time >= start_time)",
		},
		{
			name:  "users apple_user_id not empty",
			query: "ALTER TABLE users ADD CONSTRAINT IF NOT EXISTS chk_users_apple_user_id CHECK (apple_user_id != '')",
		},
	}

	for _, constraint := range constraints {
		if err := m.db.Exec(constraint.query).Error; err != nil {
			// Log warning but don't fail - some constraints might already exist
			log.Printf("Warning: Failed to create %s: %v", constraint.name, err)
		} else {
			log.Printf("✓ Added %s", constraint.name)
		}
	}

	return nil
}

// SeedTestData creates test data for development/testing
func (m *MigrationService) SeedTestData() error {
	log.Println("Seeding test data...")

	// Check if we're in a test environment (you might want to add env check)
	// Only seed if no users exist
	var userCount int64
	if err := m.db.Model(&models.User{}).Count(&userCount).Error; err != nil {
		return fmt.Errorf("failed to count users: %w", err)
	}

	if userCount > 0 {
		log.Println("Users already exist, skipping seed data")
		return nil
	}

	// Create test users
	testUsers := []models.User{
		{
			AppleUserID: "test.apple.user.1",
			Email:       "<EMAIL>",
			CreatedAt:   time.Now().Add(-24 * time.Hour),
			UpdatedAt:   time.Now().Add(-24 * time.Hour),
		},
		{
			AppleUserID: "test.apple.user.2", 
			Email:       "<EMAIL>",
			CreatedAt:   time.Now().Add(-12 * time.Hour),
			UpdatedAt:   time.Now().Add(-12 * time.Hour),
		},
	}

	for _, user := range testUsers {
		if err := m.db.Create(&user).Error; err != nil {
			return fmt.Errorf("failed to create test user %s: %w", user.AppleUserID, err)
		}
		log.Printf("✓ Created test user: %s", user.AppleUserID)

		// Create some test sessions for each user
		testSessions := []models.Session{
			{
				SessionID:    fmt.Sprintf("test-session-%d-1", user.ID),
				UserID:       user.AppleUserID,
				StartTime:    time.Now().Add(-2 * time.Hour),
				EndTime:      timePtr(time.Now().Add(-2*time.Hour + 5*time.Minute)),
				DurationSecs: intPtr(300), // 5 minutes
				CreatedAt:    time.Now().Add(-2 * time.Hour),
				UpdatedAt:    time.Now().Add(-2*time.Hour + 5*time.Minute),
			},
			{
				SessionID:    fmt.Sprintf("test-session-%d-2", user.ID),
				UserID:       user.AppleUserID,
				StartTime:    time.Now().Add(-1 * time.Hour),
				EndTime:      timePtr(time.Now().Add(-1*time.Hour + 10*time.Minute)),
				DurationSecs: intPtr(600), // 10 minutes
				CreatedAt:    time.Now().Add(-1 * time.Hour),
				UpdatedAt:    time.Now().Add(-1*time.Hour + 10*time.Minute),
			},
		}

		for _, session := range testSessions {
			if err := m.db.Create(&session).Error; err != nil {
				return fmt.Errorf("failed to create test session %s: %w", session.SessionID, err)
			}
			log.Printf("✓ Created test session: %s", session.SessionID)
		}
	}

	log.Println("Test data seeded successfully")
	return nil
}

// CleanupTestData removes all test data (useful for testing)
func (m *MigrationService) CleanupTestData() error {
	log.Println("Cleaning up test data...")

	// Delete test sessions
	if err := m.db.Where("session_id LIKE ?", "test-session-%").Delete(&models.Session{}).Error; err != nil {
		return fmt.Errorf("failed to delete test sessions: %w", err)
	}

	// Delete test users
	if err := m.db.Where("apple_user_id LIKE ?", "test.apple.user.%").Delete(&models.User{}).Error; err != nil {
		return fmt.Errorf("failed to delete test users: %w", err)
	}

	log.Println("Test data cleaned up successfully")
	return nil
}

// Helper functions
func timePtr(t time.Time) *time.Time {
	return &t
}

func intPtr(i int) *int {
	return &i
}