package database

import (
	"time"

	"rockerstt-backend/internal/models"
)

// Database defines the interface for database operations
type Database interface {
	// User operations
	GetUser(appleUserID string) (*models.User, error)
	CreateUser(user *models.User) error
	GetUserByID(id uint) (*models.User, error)

	// Session operations
	CreateSession(session *models.Session) error
	UpdateSession(sessionID string, endTime time.Time, duration int) error
	GetSession(sessionID string) (*models.Session, error)
	GetUserSessions(userID string) ([]models.Session, error)

	// Metrics operations
	GetUserCount() (int64, error)
	GetSessionCount() (int64, error)
	GetActiveSessionCount() (int, error)

	// Health check
	Ping() error

	// Lifecycle
	Initialize() error
	Close() error
}