package database

import (
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"rockerstt-backend/internal/models"
)

// setupTestDB creates an in-memory SQLite database for testing
func setupTestDB(t *testing.T) *DB {
	// Use shared in-memory SQLite for testing to support concurrent access
	// The ?cache=shared&_journal_mode=WAL parameters allow better concurrent access
	gormDB, err := gorm.Open(sqlite.Open("file::memory:?cache=shared&_journal_mode=WAL"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	db := &DB{gormDB}

	// Run migrations
	migrationService := NewMigrationService(db)
	err = migrationService.RunMigrations()
	require.NoError(t, err)

	return db
}

func TestDB_UserOperations(t *testing.T) {
	db := setupTestDB(t)

	t.Run("CreateUser", func(t *testing.T) {
		user := &models.User{
			AppleUserID: "test.apple.123",
			Email:       "<EMAIL>",
		}

		err := db.CreateUser(user)
		assert.NoError(t, err)
		assert.NotZero(t, user.ID)
		assert.NotZero(t, user.CreatedAt)
		assert.NotZero(t, user.UpdatedAt)
	})

	t.Run("GetUser", func(t *testing.T) {
		// Create a user first
		originalUser := &models.User{
			AppleUserID: "test.apple.456",
			Email:       "<EMAIL>",
		}
		err := db.CreateUser(originalUser)
		require.NoError(t, err)

		// Get the user
		retrievedUser, err := db.GetUser("test.apple.456")
		assert.NoError(t, err)
		assert.NotNil(t, retrievedUser)
		assert.Equal(t, originalUser.AppleUserID, retrievedUser.AppleUserID)
		assert.Equal(t, originalUser.Email, retrievedUser.Email)
	})

	t.Run("GetUser_NotFound", func(t *testing.T) {
		user, err := db.GetUser("nonexistent.user")
		assert.NoError(t, err)
		assert.Nil(t, user)
	})

	t.Run("GetUserByID", func(t *testing.T) {
		// Create a user first
		originalUser := &models.User{
			AppleUserID: "test.apple.789",
			Email:       "<EMAIL>",
		}
		err := db.CreateUser(originalUser)
		require.NoError(t, err)

		// Get the user by ID
		retrievedUser, err := db.GetUserByID(originalUser.ID)
		assert.NoError(t, err)
		assert.NotNil(t, retrievedUser)
		assert.Equal(t, originalUser.ID, retrievedUser.ID)
		assert.Equal(t, originalUser.AppleUserID, retrievedUser.AppleUserID)
	})
}

func TestDB_SessionOperations(t *testing.T) {
	db := setupTestDB(t)

	// Create a test user first
	user := &models.User{
		AppleUserID: "test.session.user",
		Email:       "<EMAIL>",
	}
	err := db.CreateUser(user)
	require.NoError(t, err)

	t.Run("CreateSession", func(t *testing.T) {
		sessionID := uuid.New().String()
		session := &models.Session{
			SessionID: sessionID,
			UserID:    user.AppleUserID,
			StartTime: time.Now(),
		}

		err := db.CreateSession(session)
		assert.NoError(t, err)
		assert.NotZero(t, session.CreatedAt)
		assert.NotZero(t, session.UpdatedAt)
	})

	t.Run("GetSession", func(t *testing.T) {
		// Create a session first
		sessionID := uuid.New().String()
		originalSession := &models.Session{
			SessionID: sessionID,
			UserID:    user.AppleUserID,
			StartTime: time.Now(),
		}
		err := db.CreateSession(originalSession)
		require.NoError(t, err)

		// Get the session
		retrievedSession, err := db.GetSession(sessionID)
		assert.NoError(t, err)
		assert.NotNil(t, retrievedSession)
		assert.Equal(t, sessionID, retrievedSession.SessionID)
		assert.Equal(t, user.AppleUserID, retrievedSession.UserID)
	})

	t.Run("UpdateSession", func(t *testing.T) {
		// Create a session first
		sessionID := uuid.New().String()
		session := &models.Session{
			SessionID: sessionID,
			UserID:    user.AppleUserID,
			StartTime: time.Now(),
		}
		err := db.CreateSession(session)
		require.NoError(t, err)

		// Update the session
		endTime := time.Now().Add(5 * time.Minute)
		duration := 300 // 5 minutes in seconds

		err = db.UpdateSession(sessionID, endTime, duration)
		assert.NoError(t, err)

		// Verify the update
		updatedSession, err := db.GetSession(sessionID)
		require.NoError(t, err)
		assert.NotNil(t, updatedSession.EndTime)
		assert.NotNil(t, updatedSession.DurationSecs)
		assert.Equal(t, duration, *updatedSession.DurationSecs)
	})

	t.Run("GetUserSessions", func(t *testing.T) {
		// Create multiple sessions for the user
		sessionIDs := []string{
			uuid.New().String(),
			uuid.New().String(),
			uuid.New().String(),
		}

		for i, sessionID := range sessionIDs {
			session := &models.Session{
				SessionID: sessionID,
				UserID:    user.AppleUserID,
				StartTime: time.Now().Add(time.Duration(i) * time.Minute),
			}
			err := db.CreateSession(session)
			require.NoError(t, err)
		}

		// Get all sessions for the user
		sessions, err := db.GetUserSessions(user.AppleUserID)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(sessions), 3) // At least the 3 we just created

		// Verify sessions are ordered by start_time DESC
		for i := 1; i < len(sessions); i++ {
			assert.True(t, sessions[i-1].StartTime.After(sessions[i].StartTime) || 
				sessions[i-1].StartTime.Equal(sessions[i].StartTime))
		}
	})
}

func TestDB_HealthCheck(t *testing.T) {
	db := setupTestDB(t)

	t.Run("Ping", func(t *testing.T) {
		err := db.Ping()
		assert.NoError(t, err)
	})
}

func TestMigrationService(t *testing.T) {
	// Create a fresh database without running migrations
	gormDB, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	db := &DB{gormDB}
	migrationService := NewMigrationService(db)

	t.Run("RunMigrations", func(t *testing.T) {
		err := migrationService.RunMigrations()
		assert.NoError(t, err)

		// Verify tables were created
		assert.True(t, db.Migrator().HasTable(&models.User{}))
		assert.True(t, db.Migrator().HasTable(&models.Session{}))
	})

	t.Run("SeedTestData", func(t *testing.T) {
		err := migrationService.SeedTestData()
		assert.NoError(t, err)

		// Verify test data was created
		var userCount int64
		err = db.Model(&models.User{}).Count(&userCount).Error
		assert.NoError(t, err)
		assert.Equal(t, int64(2), userCount)

		var sessionCount int64
		err = db.Model(&models.Session{}).Count(&sessionCount).Error
		assert.NoError(t, err)
		assert.Equal(t, int64(4), sessionCount) // 2 sessions per user
	})

	t.Run("CleanupTestData", func(t *testing.T) {
		err := migrationService.CleanupTestData()
		assert.NoError(t, err)

		// Verify test data was removed
		var userCount int64
		err = db.Model(&models.User{}).Where("apple_user_id LIKE ?", "test.apple.user.%").Count(&userCount).Error
		assert.NoError(t, err)
		assert.Equal(t, int64(0), userCount)
	})
}

func TestDatabaseService(t *testing.T) {
	db := setupTestDB(t)
	service := NewService(db, logrus.New())

	t.Run("GetOrCreateUser_NewUser", func(t *testing.T) {
		user, err := service.GetOrCreateUser("new.apple.user", "<EMAIL>")
		assert.NoError(t, err)
		assert.NotNil(t, user)
		assert.Equal(t, "new.apple.user", user.AppleUserID)
		assert.Equal(t, "<EMAIL>", user.Email)
		assert.NotZero(t, user.ID)
	})

	t.Run("GetOrCreateUser_ExistingUser", func(t *testing.T) {
		// Create user first
		firstUser, err := service.GetOrCreateUser("existing.apple.user", "<EMAIL>")
		require.NoError(t, err)

		// Try to get the same user again
		secondUser, err := service.GetOrCreateUser("existing.apple.user", "<EMAIL>")
		assert.NoError(t, err)
		assert.Equal(t, firstUser.ID, secondUser.ID)
		assert.Equal(t, firstUser.AppleUserID, secondUser.AppleUserID)
		// Email should remain the same as the original
		assert.Equal(t, "<EMAIL>", secondUser.Email)
	})

	t.Run("StartAndEndSession", func(t *testing.T) {
		// Create a user first
		user, err := service.GetOrCreateUser("session.test.user", "<EMAIL>")
		require.NoError(t, err)

		// Start a session
		sessionID, err := service.StartSession(user.AppleUserID)
		assert.NoError(t, err)
		assert.NotEmpty(t, sessionID)

		// Verify session was created
		session, err := db.GetSession(sessionID)
		assert.NoError(t, err)
		assert.NotNil(t, session)
		assert.Equal(t, user.AppleUserID, session.UserID)
		assert.Nil(t, session.EndTime)
		assert.Nil(t, session.DurationSecs)

		// End the session
		time.Sleep(1 * time.Second) // Small delay to ensure duration > 0
		err = service.EndSession(sessionID)
		assert.NoError(t, err)

		// Verify session was updated
		updatedSession, err := db.GetSession(sessionID)
		assert.NoError(t, err)
		assert.NotNil(t, updatedSession.EndTime)
		assert.NotNil(t, updatedSession.DurationSecs)
		assert.Greater(t, *updatedSession.DurationSecs, 0)
	})

	t.Run("GetUserSessions", func(t *testing.T) {
		// Create a user
		user, err := service.GetOrCreateUser("pagination.test.user", "<EMAIL>")
		require.NoError(t, err)

		// Create multiple sessions
		sessionIDs := make([]string, 5)
		for i := 0; i < 5; i++ {
			sessionID, err := service.StartSession(user.AppleUserID)
			require.NoError(t, err)
			sessionIDs[i] = sessionID
		}

		// Test pagination
		sessions, err := service.GetUserSessions(user.AppleUserID, 3, 0)
		assert.NoError(t, err)
		assert.Len(t, sessions, 3)

		sessions, err = service.GetUserSessions(user.AppleUserID, 3, 3)
		assert.NoError(t, err)
		assert.Len(t, sessions, 2)

		// Test without pagination
		allSessions, err := service.GetUserSessions(user.AppleUserID, 0, 0)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(allSessions), 5)
	})

	t.Run("HealthCheck", func(t *testing.T) {
		err := service.HealthCheck()
		assert.NoError(t, err)
	})
}

// Additional comprehensive test cases for DatabaseService

func TestDatabaseService_ErrorHandling(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	t.Run("EndSession_NonexistentSession", func(t *testing.T) {
		db := setupTestDB(t)
		service := NewService(db, logger)
		
		err := service.EndSession("nonexistent-session-id")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "session nonexistent-session-id not found")
	})

	t.Run("GetUserSessions_EmptyResult", func(t *testing.T) {
		db := setupTestDB(t)
		service := NewService(db, logger)
		
		sessions, err := service.GetUserSessions("nonexistent.user", 10, 0)
		assert.NoError(t, err)
		assert.Empty(t, sessions)
	})

	t.Run("GetUserSessions_OffsetBeyondResults", func(t *testing.T) {
		db := setupTestDB(t)
		service := NewService(db, logger)
		
		// Create a user with one session
		user, err := service.GetOrCreateUser("offset.test.user", "<EMAIL>")
		require.NoError(t, err)
		
		_, err = service.StartSession(user.AppleUserID)
		require.NoError(t, err)
		
		// Request with offset beyond available sessions
		sessions, err := service.GetUserSessions(user.AppleUserID, 10, 100)
		assert.NoError(t, err)
		assert.Empty(t, sessions)
	})
}

func TestDatabaseService_SessionManagement(t *testing.T) {
	db := setupTestDB(t)
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	service := NewService(db, logger)

	t.Run("MultipleSessionsPerUser", func(t *testing.T) {
		// Create a user
		user, err := service.GetOrCreateUser("multi.session.user", "<EMAIL>")
		require.NoError(t, err)

		// Start multiple sessions
		sessionIDs := make([]string, 3)
		for i := 0; i < 3; i++ {
			sessionID, err := service.StartSession(user.AppleUserID)
			require.NoError(t, err)
			sessionIDs[i] = sessionID
			
			// Small delay to ensure different timestamps
			time.Sleep(10 * time.Millisecond)
		}

		// End sessions at different times (ensure at least 1 second duration)
		for _, sessionID := range sessionIDs {
			time.Sleep(1100 * time.Millisecond) // Ensure at least 1 second duration
			err := service.EndSession(sessionID)
			assert.NoError(t, err)
		}

		// Verify all sessions were properly tracked
		sessions, err := service.GetUserSessions(user.AppleUserID, 0, 0)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(sessions), 3)

		// Check that ended sessions have proper duration
		endedSessions := 0
		for _, session := range sessions {
			if session.EndTime != nil && session.DurationSecs != nil {
				endedSessions++
				assert.Greater(t, *session.DurationSecs, 0)
				assert.True(t, session.EndTime.After(session.StartTime))
			}
		}
		assert.GreaterOrEqual(t, endedSessions, 3)
	})

	t.Run("SessionDurationCalculation", func(t *testing.T) {
		// Create a user
		user, err := service.GetOrCreateUser("duration.test.user", "<EMAIL>")
		require.NoError(t, err)

		// Start a session
		sessionID, err := service.StartSession(user.AppleUserID)
		require.NoError(t, err)

		// Wait for a known duration
		time.Sleep(2 * time.Second)

		// End the session
		err = service.EndSession(sessionID)
		assert.NoError(t, err)

		// Verify duration is approximately correct
		session, err := db.GetSession(sessionID)
		require.NoError(t, err)
		assert.NotNil(t, session.DurationSecs)
		assert.GreaterOrEqual(t, *session.DurationSecs, 2)
		assert.LessOrEqual(t, *session.DurationSecs, 5) // Allow some margin
	})

	t.Run("ConcurrentSessionOperations", func(t *testing.T) {
		// Create a user
		user, err := service.GetOrCreateUser("concurrent.test.user", "<EMAIL>")
		require.NoError(t, err)

		// Start multiple sessions concurrently (reduced count for SQLite limitations)
		sessionCount := 2
		sessionIDs := make(chan string, sessionCount)
		errors := make(chan error, sessionCount)

		for i := 0; i < sessionCount; i++ {
			go func(index int) {
				// Add small delay to reduce contention
				time.Sleep(time.Duration(index*50) * time.Millisecond)

				sessionID, err := service.StartSession(user.AppleUserID)
				if err != nil {
					errors <- err
					return
				}
				sessionIDs <- sessionID
			}(i)
		}

		// Collect results
		var collectedSessionIDs []string
		for i := 0; i < sessionCount; i++ {
			select {
			case sessionID := <-sessionIDs:
				collectedSessionIDs = append(collectedSessionIDs, sessionID)
			case err := <-errors:
				t.Errorf("Unexpected error: %v", err)
			case <-time.After(5 * time.Second):
				t.Fatal("Timeout waiting for session creation")
			}
		}

		// Verify all sessions were created
		assert.Len(t, collectedSessionIDs, sessionCount)

		// Verify all session IDs are unique
		sessionIDSet := make(map[string]bool)
		for _, sessionID := range collectedSessionIDs {
			assert.False(t, sessionIDSet[sessionID], "Session ID should be unique")
			sessionIDSet[sessionID] = true
		}
	})
}

func TestDatabaseService_UserManagement(t *testing.T) {
	db := setupTestDB(t)
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	service := NewService(db, logger)

	t.Run("UserCreation_WithEmptyEmail", func(t *testing.T) {
		user, err := service.GetOrCreateUser("empty.email.user", "")
		assert.NoError(t, err)
		assert.NotNil(t, user)
		assert.Equal(t, "empty.email.user", user.AppleUserID)
		assert.Equal(t, "", user.Email)
	})

	t.Run("UserCreation_WithSpecialCharacters", func(t *testing.T) {
		specialAppleID := "<EMAIL>/test_123!@#"
		specialEmail := "<EMAIL>"
		
		user, err := service.GetOrCreateUser(specialAppleID, specialEmail)
		assert.NoError(t, err)
		assert.NotNil(t, user)
		assert.Equal(t, specialAppleID, user.AppleUserID)
		assert.Equal(t, specialEmail, user.Email)
	})

	t.Run("UserRetrieval_CaseInsensitive", func(t *testing.T) {
		// Create user with lowercase
		originalUser, err := service.GetOrCreateUser("case.test.user", "<EMAIL>")
		require.NoError(t, err)

		// Try to get with same case (should return existing)
		sameUser, err := service.GetOrCreateUser("case.test.user", "<EMAIL>")
		assert.NoError(t, err)
		assert.Equal(t, originalUser.ID, sameUser.ID)
	})

	t.Run("ConcurrentUserCreation", func(t *testing.T) {
		userCount := 2
		users := make(chan *models.User, userCount)
		errors := make(chan error, userCount)

		// Try to create the same user concurrently
		for i := 0; i < userCount; i++ {
			go func(index int) {
				// Add small delay to reduce contention
				time.Sleep(time.Duration(index*50) * time.Millisecond)

				user, err := service.GetOrCreateUser("concurrent.user", "<EMAIL>")
				if err != nil {
					errors <- err
					return
				}
				users <- user
			}(i)
		}

		// Collect results
		var collectedUsers []*models.User
		for i := 0; i < userCount; i++ {
			select {
			case user := <-users:
				collectedUsers = append(collectedUsers, user)
			case err := <-errors:
				t.Errorf("Unexpected error: %v", err)
			case <-time.After(5 * time.Second):
				t.Fatal("Timeout waiting for user creation")
			}
		}

		// All should return the same user (same ID)
		assert.Len(t, collectedUsers, userCount)
		firstUserID := collectedUsers[0].ID
		for _, user := range collectedUsers {
			assert.Equal(t, firstUserID, user.ID)
			assert.Equal(t, "concurrent.user", user.AppleUserID)
		}
	})
}

func TestDatabaseService_PerformanceMetrics(t *testing.T) {
	db := setupTestDB(t)
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	service := NewService(db, logger)

	t.Run("BulkUserCreation_Performance", func(t *testing.T) {
		userCount := 100
		start := time.Now()

		for i := 0; i < userCount; i++ {
			_, err := service.GetOrCreateUser(
				fmt.Sprintf("bulk.user.%d", i),
				fmt.Sprintf("<EMAIL>", i),
			)
			require.NoError(t, err)
		}

		duration := time.Since(start)
		avgTimePerUser := duration / time.Duration(userCount)
		
		// Should be reasonably fast (less than 10ms per user on average)
		assert.Less(t, avgTimePerUser, 10*time.Millisecond, 
			"User creation should be fast, took %v per user", avgTimePerUser)
	})

	t.Run("BulkSessionCreation_Performance", func(t *testing.T) {
		// Create a user first
		user, err := service.GetOrCreateUser("bulk.session.user", "<EMAIL>")
		require.NoError(t, err)

		sessionCount := 100
		start := time.Now()

		for i := 0; i < sessionCount; i++ {
			_, err := service.StartSession(user.AppleUserID)
			require.NoError(t, err)
		}

		duration := time.Since(start)
		avgTimePerSession := duration / time.Duration(sessionCount)
		
		// Should be reasonably fast (less than 5ms per session on average)
		assert.Less(t, avgTimePerSession, 5*time.Millisecond,
			"Session creation should be fast, took %v per session", avgTimePerSession)
	})
}

// TestMain sets up and tears down the test environment
func TestMain(m *testing.M) {
	// Run tests
	code := m.Run()
	
	// Exit with the same code as the tests
	os.Exit(code)
}