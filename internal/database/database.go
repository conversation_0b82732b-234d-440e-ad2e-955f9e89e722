package database

import (
	"fmt"
	"log"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"rockerstt-backend/internal/models"
)

// DB holds the database connection and implements the Database interface
type DB struct {
	*gorm.DB
}

// New creates a new database connection with connection pooling
func New(databaseURL string) (*DB, error) {
	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Set connection pool settings
	sqlDB.SetMaxIdleConns(10)                  // Maximum number of idle connections
	sqlDB.SetMaxOpenConns(100)                 // Maximum number of open connections
	sqlDB.SetConnMaxLifetime(time.Hour)        // Maximum connection lifetime
	sqlDB.SetConnMaxIdleTime(30 * time.Minute) // Maximum idle time

	return &DB{db}, nil
}

// AutoMigrate runs the auto-migration for all models
func (db *DB) AutoMigrate() error {
	log.Println("Running database migrations...")

	// Create tables manually to avoid GORM auto-migration issues
	if err := db.createTablesManually(); err != nil {
		return fmt.Errorf("failed to create tables manually: %w", err)
	}

	log.Println("Database migrations completed successfully")
	return nil
}

// createTablesManually creates tables using raw SQL to avoid GORM issues
func (db *DB) createTablesManually() error {
	// Create users table
	userTableSQL := `
		CREATE TABLE IF NOT EXISTS users (
			id SERIAL PRIMARY KEY,
			apple_user_id VARCHAR(255) NOT NULL UNIQUE,
			email VARCHAR(255),
			created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
		);
		CREATE UNIQUE INDEX IF NOT EXISTS idx_users_apple_user_id ON users(apple_user_id);
		CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
	`

	if err := db.DB.Exec(userTableSQL).Error; err != nil {
		return fmt.Errorf("failed to create users table: %w", err)
	}
	log.Println("Users table created successfully")

	// Create sessions table
	sessionTableSQL := `
		CREATE TABLE IF NOT EXISTS sessions (
			session_id VARCHAR(36) PRIMARY KEY,
			user_id VARCHAR(255) NOT NULL,
			start_time TIMESTAMP WITH TIME ZONE NOT NULL,
			end_time TIMESTAMP WITH TIME ZONE,
			duration_secs INTEGER,
			created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
		);
		CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
		CREATE INDEX IF NOT EXISTS idx_sessions_start_time ON sessions(start_time);
	`

	if err := db.DB.Exec(sessionTableSQL).Error; err != nil {
		return fmt.Errorf("failed to create sessions table: %w", err)
	}
	log.Println("Sessions table created successfully")

	return nil
}

// CreateIndexes creates additional indexes for performance
func (db *DB) CreateIndexes() error {
	log.Println("Creating database indexes...")

	indexes := []struct {
		name  string
		query string
	}{
		{
			name:  "sessions user_id index",
			query: "CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id)",
		},
		{
			name:  "sessions start_time index",
			query: "CREATE INDEX IF NOT EXISTS idx_sessions_start_time ON sessions(start_time)",
		},
		{
			name:  "users apple_user_id index", 
			query: "CREATE INDEX IF NOT EXISTS idx_users_apple_user_id ON users(apple_user_id)",
		},
		{
			name:  "users email index",
			query: "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
		},
		{
			name:  "sessions composite index for user queries",
			query: "CREATE INDEX IF NOT EXISTS idx_sessions_user_start ON sessions(user_id, start_time DESC)",
		},
	}

	for _, idx := range indexes {
		if err := db.Exec(idx.query).Error; err != nil {
			return fmt.Errorf("failed to create %s: %w", idx.name, err)
		}
		log.Printf("Created %s successfully", idx.name)
	}

	log.Println("Database indexes created successfully")
	return nil
}

// Initialize runs migrations and creates indexes
func (db *DB) Initialize() error {
	if err := db.AutoMigrate(); err != nil {
		return err
	}

	if err := db.CreateIndexes(); err != nil {
		return err
	}

	return nil
}

// GetUser retrieves a user by Apple User ID
func (db *DB) GetUser(appleUserID string) (*models.User, error) {
	var user models.User
	err := db.Where("apple_user_id = ?", appleUserID).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // User not found, return nil without error
		}
		return nil, fmt.Errorf("failed to get user by apple_user_id %s: %w", appleUserID, err)
	}
	return &user, nil
}

// CreateUser creates a new user in the database
func (db *DB) CreateUser(user *models.User) error {
	if err := db.Create(user).Error; err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}
	return nil
}

// GetUserByID retrieves a user by their ID
func (db *DB) GetUserByID(id uint) (*models.User, error) {
	var user models.User
	err := db.First(&user, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // User not found, return nil without error
		}
		return nil, fmt.Errorf("failed to get user by id %d: %w", id, err)
	}
	return &user, nil
}

// CreateSession creates a new session in the database
func (db *DB) CreateSession(session *models.Session) error {
	if err := db.Create(session).Error; err != nil {
		return fmt.Errorf("failed to create session: %w", err)
	}
	return nil
}

// UpdateSession updates a session with end time and duration
func (db *DB) UpdateSession(sessionID string, endTime time.Time, duration int) error {
	err := db.Model(&models.Session{}).
		Where("session_id = ?", sessionID).
		Updates(map[string]interface{}{
			"end_time":      endTime,
			"duration_secs": duration,
			"updated_at":    time.Now(),
		}).Error
	
	if err != nil {
		return fmt.Errorf("failed to update session %s: %w", sessionID, err)
	}
	return nil
}

// GetSession retrieves a session by session ID
func (db *DB) GetSession(sessionID string) (*models.Session, error) {
	var session models.Session
	err := db.Where("session_id = ?", sessionID).First(&session).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // Session not found, return nil without error
		}
		return nil, fmt.Errorf("failed to get session %s: %w", sessionID, err)
	}
	return &session, nil
}

// GetUserSessions retrieves all sessions for a user
func (db *DB) GetUserSessions(userID string) ([]models.Session, error) {
	var sessions []models.Session
	err := db.Where("user_id = ?", userID).Order("start_time DESC").Find(&sessions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get sessions for user %s: %w", userID, err)
	}
	return sessions, nil
}

// GetUserCount returns the total number of users
func (db *DB) GetUserCount() (int64, error) {
	var count int64
	err := db.Model(&models.User{}).Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count users: %w", err)
	}
	return count, nil
}

// GetSessionCount returns the total number of sessions
func (db *DB) GetSessionCount() (int64, error) {
	var count int64
	err := db.Model(&models.Session{}).Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count sessions: %w", err)
	}
	return count, nil
}

// GetActiveSessionCount returns the number of active sessions (sessions without end time)
func (db *DB) GetActiveSessionCount() (int, error) {
	var count int64
	err := db.Model(&models.Session{}).Where("end_time IS NULL").Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count active sessions: %w", err)
	}
	return int(count), nil
}

// Ping checks if the database connection is alive
func (db *DB) Ping() error {
	sqlDB, err := db.DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	return nil
}

// Close closes the database connection
func (db *DB) Close() error {
	sqlDB, err := db.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}