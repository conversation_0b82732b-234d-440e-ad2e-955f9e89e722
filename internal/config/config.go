package config

import (
	"os"
	"strconv"
)

// Config holds all configuration for the application
type Config struct {
	Server   ServerConfig
	Database DatabaseConfig
	Logging  LoggingConfig
	Token    TokenConfig
	Apple    AppleConfig
	FunASR   FunASRConfig
}

// ServerConfig holds server-related configuration
type ServerConfig struct {
	Port string
	Host string
}

// DatabaseConfig holds database-related configuration
type DatabaseConfig struct {
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
	SSLMode  string
}

// LoggingConfig holds logging-related configuration
type LoggingConfig struct {
	Level  string
	Format string
}

// TokenConfig holds token-related configuration
type TokenConfig struct {
	HMACSecret    string
	ExpiryMinutes int
}

// AppleConfig holds Apple Sign-In related configuration
type AppleConfig struct {
	BundleID string
	JWKUrl   string
}

// FunASRConfig holds FunASR server configuration
type FunASRConfig struct {
	URL string
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	cfg := &Config{
		Server: ServerConfig{
			Port: getEnv("SERVER_PORT", "8080"),
			Host: getEnv("SERVER_HOST", "0.0.0.0"),
		},
		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "5432"),
			User:     getEnv("DB_USER", "postgres"),
			Password: getEnv("DB_PASSWORD", ""),
			DBName:   getEnv("DB_NAME", "rockerstt"),
			SSLMode:  getEnv("DB_SSLMODE", "disable"),
		},
		Logging: LoggingConfig{
			Level:  getEnv("LOG_LEVEL", "info"),
			Format: getEnv("LOG_FORMAT", "json"),
		},
		Token: TokenConfig{
			HMACSecret:    getEnv("HMAC_SECRET", ""),
			ExpiryMinutes: getEnvAsInt("TOKEN_EXPIRY_MINS", 5),
		},
		Apple: AppleConfig{
			BundleID: getEnv("APPLE_BUNDLE_ID", ""),
			JWKUrl:   getEnv("APPLE_JWK_URL", "https://appleid.apple.com/auth/keys"),
		},
		FunASR: FunASRConfig{
			URL: getEnv("FUNASR_URL", "ws://host.docker.internal:10096"),
		},
	}

	return cfg, nil
}

// getEnv gets an environment variable with a fallback value
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}

// getEnvAsInt gets an environment variable as integer with a fallback value
func getEnvAsInt(key string, fallback int) int {
	if value := os.Getenv(key); value != "" {
		if intVal, err := strconv.Atoi(value); err == nil {
			return intVal
		}
	}
	return fallback
}