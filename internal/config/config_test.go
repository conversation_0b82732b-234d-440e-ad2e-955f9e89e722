package config

import (
	"net/url"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestConfigLoad tests the basic configuration loading
func TestConfigLoad(t *testing.T) {
	cfg, err := Load()
	require.NoError(t, err, "Configuration should load without error")
	require.NotNil(t, cfg, "Configuration should not be nil")
	
	// Test that all sections are initialized
	assert.NotNil(t, cfg.Server, "Server config should be initialized")
	assert.NotNil(t, cfg.Database, "Database config should be initialized")
	assert.NotNil(t, cfg.Logging, "Logging config should be initialized")
	assert.NotNil(t, cfg.Token, "Token config should be initialized")
	assert.NotNil(t, cfg.Apple, "Apple config should be initialized")
	assert.NotNil(t, cfg.FunASR, "FunASR config should be initialized")
}

// TestFunASRConfigDefaults tests FunASR configuration defaults
func TestFunASRConfigDefaults(t *testing.T) {
	// Clear any existing FUNASR_URL environment variable
	originalURL := os.Getenv("FUNASR_URL")
	os.Unsetenv("FUNASR_URL")
	defer func() {
		if originalURL != "" {
			os.Setenv("FUNASR_URL", originalURL)
		}
	}()
	
	cfg, err := Load()
	require.NoError(t, err)
	
	// Test default FunASR URL
	expectedURL := "ws://host.docker.internal:10096"
	assert.Equal(t, expectedURL, cfg.FunASR.URL, 
		"Default FunASR URL should be ws://host.docker.internal:10096")
	
	// Verify URL is valid WebSocket URL
	parsedURL, err := url.Parse(cfg.FunASR.URL)
	require.NoError(t, err, "Default FunASR URL should be valid")
	assert.Equal(t, "ws", parsedURL.Scheme, "Default FunASR URL should use WebSocket scheme")
	assert.Equal(t, "host.docker.internal:10096", parsedURL.Host, "Default FunASR URL should use correct host and port")
}

// TestFunASRConfigEnvironmentOverride tests FunASR configuration with environment variables
func TestFunASRConfigEnvironmentOverride(t *testing.T) {
	testCases := []struct {
		name        string
		envValue    string
		expected    string
		shouldParse bool
	}{
		{
			name:        "Custom localhost URL",
			envValue:    "ws://localhost:10096",
			expected:    "ws://localhost:10096",
			shouldParse: true,
		},
		{
			name:        "Custom secure WebSocket URL",
			envValue:    "wss://funasr.example.com:10096",
			expected:    "wss://funasr.example.com:10096",
			shouldParse: true,
		},
		{
			name:        "Custom IP address",
			envValue:    "ws://*************:10096",
			expected:    "ws://*************:10096",
			shouldParse: true,
		},
		{
			name:        "Invalid URL",
			envValue:    "not-a-valid-url",
			expected:    "not-a-valid-url",
			shouldParse: false,
		},
	}
	
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Set environment variable
			os.Setenv("FUNASR_URL", tc.envValue)
			defer os.Unsetenv("FUNASR_URL")
			
			cfg, err := Load()
			require.NoError(t, err)
			
			assert.Equal(t, tc.expected, cfg.FunASR.URL, 
				"FunASR URL should match environment variable")
			
			// Test URL parsing
			parsedURL, parseErr := url.Parse(cfg.FunASR.URL)
			if tc.shouldParse {
				assert.NoError(t, parseErr, "Valid URL should parse without error")
				assert.NotEmpty(t, parsedURL.Host, "Valid URL should have host")
			} else {
				// Invalid URLs might still parse but won't have expected structure
				if parseErr == nil {
					// If it parses, it should at least not be a valid WebSocket URL
					assert.NotContains(t, []string{"ws", "wss"}, parsedURL.Scheme, 
						"Invalid URL should not have WebSocket scheme")
				}
			}
		})
	}
}

// TestServerConfigDefaults tests server configuration defaults
func TestServerConfigDefaults(t *testing.T) {
	// Clear environment variables
	originalPort := os.Getenv("SERVER_PORT")
	originalHost := os.Getenv("SERVER_HOST")
	os.Unsetenv("SERVER_PORT")
	os.Unsetenv("SERVER_HOST")
	defer func() {
		if originalPort != "" {
			os.Setenv("SERVER_PORT", originalPort)
		}
		if originalHost != "" {
			os.Setenv("SERVER_HOST", originalHost)
		}
	}()
	
	cfg, err := Load()
	require.NoError(t, err)
	
	assert.Equal(t, "8080", cfg.Server.Port, "Default server port should be 8080")
	assert.Equal(t, "0.0.0.0", cfg.Server.Host, "Default server host should be 0.0.0.0")
}

// TestDatabaseConfigDefaults tests database configuration defaults
func TestDatabaseConfigDefaults(t *testing.T) {
	// Clear database environment variables
	envVars := []string{"DB_HOST", "DB_PORT", "DB_USER", "DB_PASSWORD", "DB_NAME", "DB_SSLMODE"}
	originalValues := make(map[string]string)
	
	for _, envVar := range envVars {
		originalValues[envVar] = os.Getenv(envVar)
		os.Unsetenv(envVar)
	}
	
	defer func() {
		for envVar, originalValue := range originalValues {
			if originalValue != "" {
				os.Setenv(envVar, originalValue)
			}
		}
	}()
	
	cfg, err := Load()
	require.NoError(t, err)
	
	assert.Equal(t, "localhost", cfg.Database.Host, "Default database host should be localhost")
	assert.Equal(t, "5432", cfg.Database.Port, "Default database port should be 5432")
	assert.Equal(t, "postgres", cfg.Database.User, "Default database user should be postgres")
	assert.Equal(t, "", cfg.Database.Password, "Default database password should be empty")
	assert.Equal(t, "rockerstt", cfg.Database.DBName, "Default database name should be rockerstt")
	assert.Equal(t, "disable", cfg.Database.SSLMode, "Default database SSL mode should be disable")
}

// TestLoggingConfigDefaults tests logging configuration defaults
func TestLoggingConfigDefaults(t *testing.T) {
	// Clear logging environment variables
	originalLevel := os.Getenv("LOG_LEVEL")
	originalFormat := os.Getenv("LOG_FORMAT")
	os.Unsetenv("LOG_LEVEL")
	os.Unsetenv("LOG_FORMAT")
	defer func() {
		if originalLevel != "" {
			os.Setenv("LOG_LEVEL", originalLevel)
		}
		if originalFormat != "" {
			os.Setenv("LOG_FORMAT", originalFormat)
		}
	}()
	
	cfg, err := Load()
	require.NoError(t, err)
	
	assert.Equal(t, "info", cfg.Logging.Level, "Default log level should be info")
	assert.Equal(t, "json", cfg.Logging.Format, "Default log format should be json")
}

// TestTokenConfigDefaults tests token configuration defaults
func TestTokenConfigDefaults(t *testing.T) {
	// Clear token environment variables
	originalSecret := os.Getenv("HMAC_SECRET")
	originalExpiry := os.Getenv("TOKEN_EXPIRY_MINS")
	os.Unsetenv("HMAC_SECRET")
	os.Unsetenv("TOKEN_EXPIRY_MINS")
	defer func() {
		if originalSecret != "" {
			os.Setenv("HMAC_SECRET", originalSecret)
		}
		if originalExpiry != "" {
			os.Setenv("TOKEN_EXPIRY_MINS", originalExpiry)
		}
	}()
	
	cfg, err := Load()
	require.NoError(t, err)
	
	assert.Equal(t, "", cfg.Token.HMACSecret, "Default HMAC secret should be empty")
	assert.Equal(t, 5, cfg.Token.ExpiryMinutes, "Default token expiry should be 5 minutes")
}

// TestAppleConfigDefaults tests Apple configuration defaults
func TestAppleConfigDefaults(t *testing.T) {
	// Clear Apple environment variables
	originalBundleID := os.Getenv("APPLE_BUNDLE_ID")
	originalJWKURL := os.Getenv("APPLE_JWK_URL")
	os.Unsetenv("APPLE_BUNDLE_ID")
	os.Unsetenv("APPLE_JWK_URL")
	defer func() {
		if originalBundleID != "" {
			os.Setenv("APPLE_BUNDLE_ID", originalBundleID)
		}
		if originalJWKURL != "" {
			os.Setenv("APPLE_JWK_URL", originalJWKURL)
		}
	}()
	
	cfg, err := Load()
	require.NoError(t, err)
	
	assert.Equal(t, "", cfg.Apple.BundleID, "Default Apple bundle ID should be empty")
	assert.Equal(t, "https://appleid.apple.com/auth/keys", cfg.Apple.JWKUrl, 
		"Default Apple JWK URL should be Apple's endpoint")
}

// TestConfigValidation tests configuration validation
func TestConfigValidation(t *testing.T) {
	cfg, err := Load()
	require.NoError(t, err)
	
	// Test FunASR URL validation
	if cfg.FunASR.URL != "" {
		parsedURL, err := url.Parse(cfg.FunASR.URL)
		assert.NoError(t, err, "FunASR URL should be valid")
		assert.Contains(t, []string{"ws", "wss"}, parsedURL.Scheme, 
			"FunASR URL should use WebSocket protocol")
	}
	
	// Test Apple JWK URL validation
	if cfg.Apple.JWKUrl != "" {
		parsedURL, err := url.Parse(cfg.Apple.JWKUrl)
		assert.NoError(t, err, "Apple JWK URL should be valid")
		assert.Contains(t, []string{"http", "https"}, parsedURL.Scheme, 
			"Apple JWK URL should use HTTP protocol")
	}
}

// TestGetEnvFunction tests the getEnv helper function
func TestGetEnvFunction(t *testing.T) {
	testKey := "TEST_CONFIG_KEY"
	testValue := "test-value"
	fallbackValue := "fallback-value"
	
	// Test with environment variable set
	os.Setenv(testKey, testValue)
	defer os.Unsetenv(testKey)
	
	result := getEnv(testKey, fallbackValue)
	assert.Equal(t, testValue, result, "Should return environment variable value")
	
	// Test with environment variable unset
	os.Unsetenv(testKey)
	result = getEnv(testKey, fallbackValue)
	assert.Equal(t, fallbackValue, result, "Should return fallback value when env var is unset")
	
	// Test with empty environment variable
	os.Setenv(testKey, "")
	result = getEnv(testKey, fallbackValue)
	assert.Equal(t, fallbackValue, result, "Should return fallback value when env var is empty")
}

// TestGetEnvAsIntFunction tests the getEnvAsInt helper function
func TestGetEnvAsIntFunction(t *testing.T) {
	testKey := "TEST_CONFIG_INT_KEY"
	fallbackValue := 42
	
	testCases := []struct {
		name     string
		envValue string
		expected int
	}{
		{
			name:     "Valid integer",
			envValue: "123",
			expected: 123,
		},
		{
			name:     "Invalid integer",
			envValue: "not-a-number",
			expected: fallbackValue,
		},
		{
			name:     "Empty string",
			envValue: "",
			expected: fallbackValue,
		},
		{
			name:     "Zero value",
			envValue: "0",
			expected: 0,
		},
	}
	
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if tc.envValue == "" {
				os.Unsetenv(testKey)
			} else {
				os.Setenv(testKey, tc.envValue)
			}
			defer os.Unsetenv(testKey)
			
			result := getEnvAsInt(testKey, fallbackValue)
			assert.Equal(t, tc.expected, result, "Should return expected integer value")
		})
	}
}
