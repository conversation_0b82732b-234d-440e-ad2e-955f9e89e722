package server

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"rockerstt-backend/internal/config"
	"rockerstt-backend/internal/errors"
	"rockerstt-backend/internal/logger"
)

func TestHandleToken_InvalidRequest(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create test config
	cfg := &config.Config{
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "json",
		},
	}

	// Create server with minimal config
	log := logger.New(cfg)
	server := &Server{
		config:       cfg,
		logger:       log,
		router:       gin.New(),
		errorHandler: errors.NewErrorHandler(log.Logger),
	}

	// Create invalid request (missing identityToken)
	req, _ := http.NewRequest("POST", "/api/token", bytes.NewBuffer([]byte("{}")))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	server.handleToken(c)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response errors.ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "validation_error", response.Error)
	assert.Equal(t, "INVALID_REQUEST_BODY", response.Code)
}

func TestHandleToken_MalformedJSON(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create test config
	cfg := &config.Config{
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "json",
		},
	}

	log := logger.New(cfg)
	server := &Server{
		config:       cfg,
		logger:       log,
		router:       gin.New(),
		errorHandler: errors.NewErrorHandler(log.Logger),
	}

	// Create request with malformed JSON
	req, _ := http.NewRequest("POST", "/api/token", bytes.NewBuffer([]byte("invalid json")))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	server.handleToken(c)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response errors.ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "validation_error", response.Error)
	assert.Equal(t, "INVALID_REQUEST_BODY", response.Code)
}

func TestHandleWebSocket_MissingToken(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create test config
	cfg := &config.Config{
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "json",
		},
	}

	log := logger.New(cfg)
	server := &Server{
		config:       cfg,
		logger:       log,
		router:       gin.New(),
		errorHandler: errors.NewErrorHandler(log.Logger),
	}

	// Create request without token parameter
	req, _ := http.NewRequest("GET", "/ws/asr", nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	server.handleWebSocket(c)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response errors.ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "validation_error", response.Error)
	assert.Equal(t, "MISSING_TOKEN", response.Code)
	assert.Equal(t, "Token parameter is required", response.Message)
}