package server

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"rockerstt-backend/internal/auth"
	"rockerstt-backend/internal/config"
	"rockerstt-backend/internal/database"
	"rockerstt-backend/internal/errors"
	"rockerstt-backend/internal/logger"
	"rockerstt-backend/internal/token"
)

// Server represents the HTTP server
type Server struct {
	config       *config.Config
	logger       *logger.Logger
	router       *gin.Engine
	authSvc      *auth.AuthService
	tokenSvc     *token.TokenService
	db           database.Database
	dbSvc        *database.Service
	errorHandler *errors.ErrorHandler
	startTime    time.Time
}

// New creates a new server instance
func New(cfg *config.Config, authSvc *auth.AuthService, tokenSvc *token.TokenService, db database.Database) *Server {
	log := logger.New(cfg)
	
	// Set gin mode based on log level
	if cfg.Logging.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	
	// Create error handler
	errorHandler := errors.NewErrorHandler(log.Logger)
	
	// Add middleware
	router.Use(errorHandler.Middleware()) // Custom recovery with structured errors
	router.Use(securityHeadersMiddleware()) // Security headers
	router.Use(corsMiddleware())
	router.Use(inputValidationMiddleware()) // Input validation and sanitization
	router.Use(timeoutMiddleware(30 * time.Second)) // Request timeout
	router.Use(rateLimitMiddleware(60, 10, errorHandler)) // Rate limiting: 60 req/min, burst 10
	router.Use(loggingMiddleware(log))

	// Create database service
	dbSvc := database.NewService(db, log.Logger)

	server := &Server{
		config:       cfg,
		logger:       log,
		router:       router,
		authSvc:      authSvc,
		tokenSvc:     tokenSvc,
		db:           db,
		dbSvc:        dbSvc,
		errorHandler: errorHandler,
		startTime:    time.Now(),
	}

	server.setupRoutes()
	return server
}

// setupRoutes configures all routes
func (s *Server) setupRoutes() {
	// Health check endpoints
	s.router.GET("/health", s.handleHealth)
	s.router.GET("/ready", s.handleReady)
	s.router.GET("/metrics", s.handleMetrics)
	
	// API routes
	api := s.router.Group("/api")
	{
		api.POST("/token", s.handleToken)
	}
	
	// WebSocket routes
	ws := s.router.Group("/ws")
	{
		ws.GET("/asr", s.handleWebSocket)
	}
	
	// API version group (for future versioning)
	v1 := s.router.Group("/api/v1")
	{
		v1.GET("/status", s.statusCheck)
	}
}

// Start starts the HTTP server
func (s *Server) Start() error {
	addr := fmt.Sprintf("%s:%s", s.config.Server.Host, s.config.Server.Port)
	
	srv := &http.Server{
		Addr:    addr,
		Handler: s.router,
	}

	// Start server in a goroutine
	go func() {
		s.logger.WithField("address", addr).Info("Starting HTTP server")
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			s.logger.WithError(err).Fatal("Failed to start server")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	s.logger.Info("Shutting down server...")

	// Give outstanding requests 30 seconds to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := srv.Shutdown(ctx); err != nil {
		s.logger.WithError(err).Error("Server forced to shutdown")
		return err
	}

	s.logger.Info("Server exited")
	return nil
}