package server

import (
	"context"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"rockerstt-backend/internal/errors"
	"rockerstt-backend/internal/logger"
)

// corsMiddleware handles CORS headers
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.<PERSON><PERSON>("Access-Control-Expose-Headers", "Content-Length")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// loggingMiddleware logs HTTP requests with comprehensive details
func loggingMiddleware(log *logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery
		method := c.Request.Method
		
		// Log incoming request
		requestEntry := log.WithFields(logrus.Fields{
			"method":       method,
			"path":         path,
			"query":        raw,
			"ip":           c.ClientIP(),
			"user_agent":   c.Request.UserAgent(),
			"content_type": c.Request.Header.Get("Content-Type"),
			"referer":      c.Request.Header.Get("Referer"),
			"request_id":   c.GetHeader("X-Request-ID"), // If using request ID middleware
		})
		
		// Don't log sensitive endpoints at debug level
		if path == "/api/token" {
			requestEntry.Info("Authentication request received")
		} else if path == "/ws/asr" {
			requestEntry.Info("WebSocket upgrade request received")
		} else {
			requestEntry.Debug("HTTP request received")
		}

		// Process request
		c.Next()

		// Calculate latency
		latency := time.Since(start)
		latencyMs := float64(latency.Nanoseconds()) / 1e6

		// Get response details
		statusCode := c.Writer.Status()
		responseSize := c.Writer.Size()

		// Build comprehensive log entry
		responseEntry := log.WithFields(logrus.Fields{
			"method":        method,
			"path":          path,
			"query":         raw,
			"status":        statusCode,
			"latency_ms":    latencyMs,
			"latency":       latency.String(),
			"response_size": responseSize,
			"ip":            c.ClientIP(),
			"user_agent":    c.Request.UserAgent(),
		})

		// Add error information if present
		if len(c.Errors) > 0 {
			responseEntry = responseEntry.WithField("errors", c.Errors.String())
		}

		// Log response based on status code and endpoint
		switch {
		case statusCode >= 500:
			responseEntry.Error("HTTP request completed with server error")
		case statusCode >= 400:
			if path == "/api/token" {
				responseEntry.Warn("Authentication request failed")
			} else if path == "/ws/asr" {
				responseEntry.Warn("WebSocket upgrade failed")
			} else {
				responseEntry.Warn("HTTP request completed with client error")
			}
		case statusCode >= 300:
			responseEntry.Info("HTTP request completed with redirect")
		default:
			if path == "/api/token" {
				responseEntry.Info("Authentication request successful")
			} else if path == "/ws/asr" {
				responseEntry.Info("WebSocket upgrade successful")
			} else if path == "/health" || path == "/health/ready" || path == "/health/live" {
				responseEntry.Debug("Health check completed")
			} else {
				responseEntry.Info("HTTP request completed successfully")
			}
		}
	}
}

// RateLimiter implements a simple token bucket rate limiter per IP
type RateLimiter struct {
	clients map[string]*ClientLimiter
	mutex   sync.RWMutex
	rate    int           // requests per minute
	burst   int           // maximum burst size
	cleanup time.Duration // cleanup interval for inactive clients
}

// ClientLimiter tracks rate limiting for a specific client
type ClientLimiter struct {
	tokens    int
	lastSeen  time.Time
	lastRefill time.Time
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(requestsPerMinute, burstSize int) *RateLimiter {
	rl := &RateLimiter{
		clients: make(map[string]*ClientLimiter),
		rate:    requestsPerMinute,
		burst:   burstSize,
		cleanup: 5 * time.Minute, // Clean up inactive clients every 5 minutes
	}

	// Start cleanup goroutine
	go rl.cleanupInactiveClients()

	return rl
}

// Allow checks if a request from the given IP should be allowed
func (rl *RateLimiter) Allow(ip string) bool {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	now := time.Now()
	client, exists := rl.clients[ip]

	if !exists {
		// New client
		client = &ClientLimiter{
			tokens:     rl.burst - 1, // Use one token for this request
			lastSeen:   now,
			lastRefill: now,
		}
		rl.clients[ip] = client
		return true
	}

	// Update last seen
	client.lastSeen = now

	// Refill tokens based on time elapsed
	elapsed := now.Sub(client.lastRefill)
	tokensToAdd := int(elapsed.Minutes() * float64(rl.rate))

	if tokensToAdd > 0 {
		client.tokens += tokensToAdd
		if client.tokens > rl.burst {
			client.tokens = rl.burst
		}
		client.lastRefill = now
	}

	// Check if request is allowed
	if client.tokens > 0 {
		client.tokens--
		return true
	}

	return false
}

// cleanupInactiveClients removes clients that haven't been seen recently
func (rl *RateLimiter) cleanupInactiveClients() {
	ticker := time.NewTicker(rl.cleanup)
	defer ticker.Stop()

	for range ticker.C {
		rl.mutex.Lock()
		now := time.Now()
		for ip, client := range rl.clients {
			if now.Sub(client.lastSeen) > rl.cleanup {
				delete(rl.clients, ip)
			}
		}
		rl.mutex.Unlock()
	}
}

// rateLimitMiddleware creates a rate limiting middleware
func rateLimitMiddleware(requestsPerMinute, burstSize int, errorHandler *errors.ErrorHandler) gin.HandlerFunc {
	limiter := NewRateLimiter(requestsPerMinute, burstSize)

	return func(c *gin.Context) {
		clientIP := c.ClientIP()

		if !limiter.Allow(clientIP) {
			appErr := errors.ErrRateLimitExceeded()
			errorHandler.HandleError(c, appErr)
			c.Abort()
			return
		}

		c.Next()
	}
}

// timeoutMiddleware adds request timeout handling
func timeoutMiddleware(timeout time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Set a timeout context
		ctx, cancel := context.WithTimeout(c.Request.Context(), timeout)
		defer cancel()

		// Replace the request context
		c.Request = c.Request.WithContext(ctx)

		// Create a channel to signal completion
		done := make(chan struct{})

		go func() {
			defer close(done)
			c.Next()
		}()

		select {
		case <-done:
			// Request completed normally
			return
		case <-ctx.Done():
			// Request timed out
			c.JSON(http.StatusRequestTimeout, gin.H{
				"error":   "request_timeout",
				"code":    "REQUEST_TIMEOUT",
				"message": "Request timed out",
			})
			c.Abort()
			return
		}
	}
}

// securityHeadersMiddleware adds security headers
func securityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Security headers
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Content-Security-Policy", "default-src 'self'")

		// Remove server information
		c.Header("Server", "")

		c.Next()
	}
}

// inputValidationMiddleware provides basic input validation and sanitization
func inputValidationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check content length
		if c.Request.ContentLength > 1024*1024 { // 1MB limit
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{
				"error":   "request_too_large",
				"code":    "REQUEST_TOO_LARGE",
				"message": "Request body too large",
			})
			c.Abort()
			return
		}

		// Validate content type for POST/PUT requests
		if c.Request.Method == "POST" || c.Request.Method == "PUT" {
			contentType := c.GetHeader("Content-Type")
			if contentType != "" && contentType != "application/json" {
				c.JSON(http.StatusUnsupportedMediaType, gin.H{
					"error":   "unsupported_media_type",
					"code":    "UNSUPPORTED_MEDIA_TYPE",
					"message": "Content-Type must be application/json",
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}