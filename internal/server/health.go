package server

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// ReadinessResponse represents the response structure for readiness checks
type ReadinessResponse struct {
	Status    string            `json:"status"`
	Timestamp time.Time         `json:"timestamp"`
	Checks    map[string]string `json:"checks,omitempty"`
}

// MetricsResponse represents basic metrics for monitoring
type MetricsResponse struct {
	Uptime          string `json:"uptime"`
	ActiveSessions  int    `json:"active_sessions"`
	TotalUsers      int64  `json:"total_users"`
	TotalSessions   int64  `json:"total_sessions"`
	DatabaseStatus  string `json:"database_status"`
	MemoryUsage     string `json:"memory_usage,omitempty"`
}

// handleHealth provides a basic liveness probe
func (s *Server) handleHealth(c *gin.Context) {
	response := HealthResponse{
		Status:    "healthy",
		Timestamp: time.Now().UTC(),
		Version:   "1.0.0", // This could be injected at build time
		Uptime:    time.Since(s.startTime).String(),
	}

	c.<PERSON>SON(http.StatusOK, response)
}

// handleReady provides a readiness probe that checks dependencies
func (s *Server) handleReady(c *gin.Context) {
	checks := make(map[string]string)
	status := "ready"
	httpStatus := http.StatusOK

	// Check database connectivity
	if err := s.checkDatabaseHealth(); err != nil {
		checks["database"] = "unhealthy: " + err.Error()
		status = "not ready"
		httpStatus = http.StatusServiceUnavailable
		
		s.logger.WithFields(logrus.Fields{
			"error": err.Error(),
		}).Error("Database health check failed")
	} else {
		checks["database"] = "healthy"
	}

	// Check FunASR connectivity (optional - don't fail if unavailable)
	if err := s.checkFunASRHealth(); err != nil {
		checks["funasr"] = "warning: " + err.Error()
		s.logger.WithFields(logrus.Fields{
			"error": err.Error(),
		}).Warn("FunASR health check failed")
	} else {
		checks["funasr"] = "healthy"
	}

	response := ReadinessResponse{
		Status:    status,
		Timestamp: time.Now().UTC(),
		Checks:    checks,
	}

	c.JSON(httpStatus, response)
}

// handleMetrics provides basic metrics for monitoring
func (s *Server) handleMetrics(c *gin.Context) {
	// Calculate uptime
	uptime := time.Since(s.startTime).String()

	// Get database metrics
	totalUsers, err := s.dbSvc.GetUserCount()
	if err != nil {
		s.logger.WithFields(logrus.Fields{
			"error": err.Error(),
		}).Error("Failed to get user count for metrics")
		totalUsers = -1 // Indicate error
	}

	totalSessions, err := s.dbSvc.GetSessionCount()
	if err != nil {
		s.logger.WithFields(logrus.Fields{
			"error": err.Error(),
		}).Error("Failed to get session count for metrics")
		totalSessions = -1 // Indicate error
	}

	activeSessions, err := s.dbSvc.GetActiveSessionCount()
	if err != nil {
		s.logger.WithFields(logrus.Fields{
			"error": err.Error(),
		}).Error("Failed to get active session count for metrics")
		activeSessions = -1 // Indicate error
	}

	// Check database status
	dbStatus := "healthy"
	if err := s.checkDatabaseHealth(); err != nil {
		dbStatus = "unhealthy"
	}

	response := MetricsResponse{
		Uptime:          uptime,
		ActiveSessions:  activeSessions,
		TotalUsers:      totalUsers,
		TotalSessions:   totalSessions,
		DatabaseStatus:  dbStatus,
	}

	c.JSON(http.StatusOK, response)
}

// checkDatabaseHealth verifies database connectivity
func (s *Server) checkDatabaseHealth() error {
	// Use the database service's health check
	return s.dbSvc.HealthCheck()
}

// checkFunASRHealth verifies FunASR service connectivity
func (s *Server) checkFunASRHealth() error {
	// For now, we'll just return nil since FunASR health checking
	// would require WebSocket connection testing which is complex
	// In a production environment, this could:
	// 1. Try to establish a WebSocket connection to FunASR
	// 2. Send a ping message and wait for response
	// 3. Check if the service is responding within a timeout
	
	// TODO: Implement actual FunASR health check
	return nil
}
