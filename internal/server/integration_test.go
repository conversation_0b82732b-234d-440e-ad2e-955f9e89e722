package server

import (
	"bytes"
	"crypto/rand"
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"rockerstt-backend/internal/auth"
	"rockerstt-backend/internal/config"
	"rockerstt-backend/internal/database"
	"rockerstt-backend/internal/errors"
	appLogger "rockerstt-backend/internal/logger"
	"rockerstt-backend/internal/token"
)

// setupIntegrationTest creates a complete server setup for integration testing
func setupIntegrationTest(t *testing.T) (*Server, *httptest.Server, *rsa.PrivateKey, string) {
	gin.SetMode(gin.TestMode)

	// Create test configuration
	cfg := &config.Config{
		Server: config.ServerConfig{
			Host: "localhost",
			Port: "8080",
		},
		Logging: config.LoggingConfig{
			Level:  "error", // Reduce noise in tests
			Format: "json",
		},
		Apple: config.AppleConfig{
			BundleID: "com.example.app",
		},
		Token: config.TokenConfig{
			HMACSecret:   "test-secret-for-integration-tests",
			ExpiryMinutes: 5,
		},
		FunASR: config.FunASRConfig{
			URL: "ws://host.docker.internal:10096",
		},
	}

	// Create test database
	gormDB, err := gorm.Open(sqlite.Open("file::memory:?cache=shared&_journal_mode=WAL"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	db := &database.DB{DB: gormDB}

	// Run migrations
	migrationService := database.NewMigrationService(db)
	err = migrationService.RunMigrations()
	require.NoError(t, err)

	// Create test RSA key pair for Apple token simulation
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)
	publicKey := &privateKey.PublicKey

	kid := "integration-test-key"

	// Create mock JWK response
	mockJWKResponse := createMockJWKResponse(t, kid, publicKey)

	// Create mock Apple JWK server
	jwkServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(mockJWKResponse))
	}))

	// Update config with mock JWK URL
	cfg.Apple.JWKUrl = jwkServer.URL

	// Initialize services
	log := appLogger.New(cfg)
	jwkService := auth.NewJWKService(cfg.Apple.JWKUrl, log.Logger)
	appleValidator := auth.NewAppleTokenValidator(jwkService, cfg.Apple.BundleID, log.Logger)
	authSvc := auth.NewAuthService(appleValidator, db, log.Logger)
	tokenSvc := token.NewTokenService(cfg.Token.HMACSecret, cfg.Token.ExpiryMinutes)

	// Create server
	server := New(cfg, authSvc, tokenSvc, db)

	return server, jwkServer, privateKey, kid
}

// createMockJWKResponse creates a mock JWK response for testing
func createMockJWKResponse(t *testing.T, kid string, publicKey *rsa.PublicKey) string {
	// Convert RSA public key to JWK format
	n := publicKey.N.Bytes()
	e := []byte{1, 0, 1} // 65537 in bytes

	jwkSet := map[string]interface{}{
		"keys": []map[string]interface{}{
			{
				"kty": "RSA",
				"kid": kid,
				"use": "sig",
				"alg": "RS256",
				"n":   base64URLEncode(n),
				"e":   base64URLEncode(e),
			},
		},
	}

	response, err := json.Marshal(jwkSet)
	require.NoError(t, err)
	return string(response)
}

// base64URLEncode encodes bytes to base64 URL encoding without padding
func base64URLEncode(data []byte) string {
	return base64.RawURLEncoding.EncodeToString(data)
}

// createTestAppleToken creates a valid Apple JWT token for testing
func createTestAppleToken(t *testing.T, privateKey *rsa.PrivateKey, kid string, claims map[string]interface{}) string {
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims(claims))
	token.Header["kid"] = kid

	tokenString, err := token.SignedString(privateKey)
	require.NoError(t, err)
	return tokenString
}

// TestTokenEndpoint_Integration tests the complete token endpoint flow
func TestTokenEndpoint_Integration(t *testing.T) {
	server, jwkServer, privateKey, kid := setupIntegrationTest(t)
	defer jwkServer.Close()

	t.Run("SuccessfulAuthentication", func(t *testing.T) {
		// Create valid Apple token
		now := time.Now().Unix()
		claims := map[string]interface{}{
			"sub":   "apple.user.integration.test",
			"email": "<EMAIL>",
			"exp":   now + 3600,
			"iat":   now - 60,
			"iss":   "https://appleid.apple.com",
			"aud":   "com.example.app",
		}

		appleToken := createTestAppleToken(t, privateKey, kid, claims)

		// Create request
		requestBody := map[string]string{
			"identityToken": appleToken,
		}
		requestJSON, err := json.Marshal(requestBody)
		require.NoError(t, err)

		req, err := http.NewRequest("POST", "/api/token", bytes.NewBuffer(requestJSON))
		require.NoError(t, err)
		req.Header.Set("Content-Type", "application/json")

		// Execute request
		w := httptest.NewRecorder()
		server.router.ServeHTTP(w, req)

		// Verify response
		assert.Equal(t, http.StatusOK, w.Code)

		var response TokenResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.NotEmpty(t, response.AccessToken)
		assert.Equal(t, 300, response.ExpiresIn) // 5 minutes in seconds

		// Verify token can be validated
		tokenSvc := token.NewTokenService("test-secret-for-integration-tests", 5)
		tokenClaims, err := tokenSvc.ValidateToken(response.AccessToken)
		require.NoError(t, err)
		assert.True(t, tokenClaims.Valid)
		assert.Equal(t, "apple.user.integration.test", tokenClaims.UserID)
	})

	t.Run("InvalidAppleToken", func(t *testing.T) {
		// Create request with invalid token
		requestBody := map[string]string{
			"identityToken": "invalid.token.here",
		}
		requestJSON, err := json.Marshal(requestBody)
		require.NoError(t, err)

		req, err := http.NewRequest("POST", "/api/token", bytes.NewBuffer(requestJSON))
		require.NoError(t, err)
		req.Header.Set("Content-Type", "application/json")

		// Execute request
		w := httptest.NewRecorder()
		server.router.ServeHTTP(w, req)

		// Verify error response
		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response errors.ErrorResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "authentication_error", response.Error)
		assert.Equal(t, "INVALID_APPLE_TOKEN", response.Code)
	})

	t.Run("MissingIdentityToken", func(t *testing.T) {
		// Create request without identityToken
		requestBody := map[string]string{}
		requestJSON, err := json.Marshal(requestBody)
		require.NoError(t, err)

		req, err := http.NewRequest("POST", "/api/token", bytes.NewBuffer(requestJSON))
		require.NoError(t, err)
		req.Header.Set("Content-Type", "application/json")

		// Execute request
		w := httptest.NewRecorder()
		server.router.ServeHTTP(w, req)

		// Verify error response
		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response errors.ErrorResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "validation_error", response.Error)
		assert.Equal(t, "INVALID_REQUEST_BODY", response.Code)
	})

	t.Run("ExpiredAppleToken", func(t *testing.T) {
		// Create expired Apple token
		now := time.Now().Unix()
		claims := map[string]interface{}{
			"sub":   "apple.user.expired",
			"email": "<EMAIL>",
			"exp":   now - 3600, // Expired 1 hour ago
			"iat":   now - 7200, // Issued 2 hours ago
			"iss":   "https://appleid.apple.com",
			"aud":   "com.example.app",
		}

		appleToken := createTestAppleToken(t, privateKey, kid, claims)

		// Create request
		requestBody := map[string]string{
			"identityToken": appleToken,
		}
		requestJSON, err := json.Marshal(requestBody)
		require.NoError(t, err)

		req, err := http.NewRequest("POST", "/api/token", bytes.NewBuffer(requestJSON))
		require.NoError(t, err)
		req.Header.Set("Content-Type", "application/json")

		// Execute request
		w := httptest.NewRecorder()
		server.router.ServeHTTP(w, req)

		// Verify error response
		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response errors.ErrorResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "authentication_error", response.Error)
		assert.Equal(t, "INVALID_APPLE_TOKEN", response.Code)
		assert.Contains(t, response.Message, "invalid or expired")
	})
}
