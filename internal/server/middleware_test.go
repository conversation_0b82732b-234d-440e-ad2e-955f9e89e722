package server

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"rockerstt-backend/internal/config"
	"rockerstt-backend/internal/errors"
	"rockerstt-backend/internal/logger"
)

func TestRateLimiter(t *testing.T) {
	limiter := NewRateLimiter(60, 5) // 60 requests per minute, burst of 5

	t.Run("AllowsInitialRequests", func(t *testing.T) {
		ip := "***********"
		
		// Should allow burst requests
		for i := 0; i < 5; i++ {
			assert.True(t, limiter.Allow(ip), "Request %d should be allowed", i+1)
		}
		
		// Should deny the 6th request
		assert.False(t, limiter.Allow(ip), "6th request should be denied")
	})

	t.Run("RefillsTokensOverTime", func(t *testing.T) {
		ip := "***********"
		
		// Use up all tokens
		for i := 0; i < 5; i++ {
			limiter.Allow(ip)
		}
		
		// Should be denied
		assert.False(t, limiter.Allow(ip))
		
		// Simulate time passing (this is a simplified test)
		// In a real scenario, we'd need to wait or mock time
		// For now, we'll just verify the basic functionality
	})

	t.Run("DifferentIPsIndependent", func(t *testing.T) {
		ip1 := "***********"
		ip2 := "***********"
		
		// Use up tokens for ip1
		for i := 0; i < 5; i++ {
			limiter.Allow(ip1)
		}
		
		// ip1 should be denied
		assert.False(t, limiter.Allow(ip1))
		
		// ip2 should still be allowed
		assert.True(t, limiter.Allow(ip2))
	})
}

func TestRateLimitMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	cfg := &config.Config{
		Logging: config.LoggingConfig{Level: "error"},
	}
	log := logger.New(cfg)
	errorHandler := errors.NewErrorHandler(log.Logger)

	t.Run("AllowsNormalRequests", func(t *testing.T) {
		router := gin.New()
		router.Use(rateLimitMiddleware(60, 5, errorHandler))
		router.GET("/test", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "success"})
		})

		// Make several requests that should be allowed
		for i := 0; i < 3; i++ {
			req, _ := http.NewRequest("GET", "/test", nil)
			req.RemoteAddr = "***********00:12345"
			
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
			
			assert.Equal(t, http.StatusOK, w.Code)
		}
	})

	t.Run("BlocksExcessiveRequests", func(t *testing.T) {
		router := gin.New()
		router.Use(rateLimitMiddleware(60, 2, errorHandler)) // Lower limits for testing
		router.GET("/test", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "success"})
		})

		// Make requests up to the limit
		for i := 0; i < 2; i++ {
			req, _ := http.NewRequest("GET", "/test", nil)
			req.RemoteAddr = "***********01:12345"
			
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
			
			assert.Equal(t, http.StatusOK, w.Code)
		}

		// Next request should be rate limited
		req, _ := http.NewRequest("GET", "/test", nil)
		req.RemoteAddr = "***********01:12345"
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusTooManyRequests, w.Code)
	})
}

func TestSecurityHeadersMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	router := gin.New()
	router.Use(securityHeadersMiddleware())
	router.GET("/test", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	req, _ := http.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	
	// Check security headers
	assert.Equal(t, "nosniff", w.Header().Get("X-Content-Type-Options"))
	assert.Equal(t, "DENY", w.Header().Get("X-Frame-Options"))
	assert.Equal(t, "1; mode=block", w.Header().Get("X-XSS-Protection"))
	assert.Equal(t, "strict-origin-when-cross-origin", w.Header().Get("Referrer-Policy"))
	assert.Equal(t, "default-src 'self'", w.Header().Get("Content-Security-Policy"))
	assert.Equal(t, "", w.Header().Get("Server"))
}

func TestInputValidationMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	router := gin.New()
	router.Use(inputValidationMiddleware())
	router.POST("/test", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	t.Run("AllowsValidRequests", func(t *testing.T) {
		body := `{"test": "data"}`
		req, _ := http.NewRequest("POST", "/test", strings.NewReader(body))
		req.Header.Set("Content-Type", "application/json")
		req.ContentLength = int64(len(body))
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("RejectsLargeRequests", func(t *testing.T) {
		// Create a request larger than 1MB
		largeBody := strings.Repeat("x", 1024*1024+1)
		req, _ := http.NewRequest("POST", "/test", strings.NewReader(largeBody))
		req.Header.Set("Content-Type", "application/json")
		req.ContentLength = int64(len(largeBody))
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusRequestEntityTooLarge, w.Code)
	})

	t.Run("RejectsInvalidContentType", func(t *testing.T) {
		body := `{"test": "data"}`
		req, _ := http.NewRequest("POST", "/test", strings.NewReader(body))
		req.Header.Set("Content-Type", "text/plain")
		req.ContentLength = int64(len(body))
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusUnsupportedMediaType, w.Code)
	})

	t.Run("AllowsEmptyContentType", func(t *testing.T) {
		body := `{"test": "data"}`
		req, _ := http.NewRequest("POST", "/test", strings.NewReader(body))
		// No Content-Type header set
		req.ContentLength = int64(len(body))
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusOK, w.Code)
	})
}

func TestCorsMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)
	
	router := gin.New()
	router.Use(corsMiddleware())
	router.GET("/test", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	t.Run("HandlesPreflightRequest", func(t *testing.T) {
		req, _ := http.NewRequest("OPTIONS", "/test", nil)
		req.Header.Set("Origin", "http://localhost:3000")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNoContent, w.Code)
		assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
		assert.Contains(t, w.Header().Get("Access-Control-Allow-Methods"), "GET")
		assert.Contains(t, w.Header().Get("Access-Control-Allow-Methods"), "POST")
	})

	t.Run("AllowsAllOrigins", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set("Origin", "http://localhost:3000")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
	})

	t.Run("SetsCorsHeaders", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/test", nil)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
		assert.Equal(t, "true", w.Header().Get("Access-Control-Allow-Credentials"))
		assert.Contains(t, w.Header().Get("Access-Control-Allow-Methods"), "GET")
	})
}
