package server

import (
	"net/http"
	"net/url"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"

	"rockerstt-backend/internal/errors"
)

// HealthResponse represents the health check response
type HealthResponse struct {
	Status    string    `json:"status"`
	Timestamp time.Time `json:"timestamp"`
	Version   string    `json:"version,omitempty"`
	Uptime    string    `json:"uptime,omitempty"`
}

// TokenRequest represents the request body for the token endpoint
type TokenRequest struct {
	IdentityToken string `json:"identityToken" binding:"required"`
}

// TokenResponse represents the response body for the token endpoint
type TokenResponse struct {
	AccessToken string `json:"accessToken"`
	ExpiresIn   int    `json:"expiresIn"`
}

// Note: ErrorResponse is now defined in internal/errors package

var startTime = time.Now()

// healthCheck handles basic health check
func (s *Server) healthCheck(c *gin.Context) {
	uptime := time.Since(startTime)
	
	response := HealthResponse{
		Status:    "ok",
		Timestamp: time.Now(),
		Version:   "1.0.0", // This could be loaded from build info
		Uptime:    uptime.String(),
	}

	c.JSON(http.StatusOK, response)
}

// readinessCheck handles readiness probe
func (s *Server) readinessCheck(c *gin.Context) {
	// Check database connectivity
	if err := s.db.Ping(); err != nil {
		appErr := errors.ErrDatabaseUnavailable(err)
		s.errorHandler.HandleError(c, appErr)
		return
	}
	
	response := HealthResponse{
		Status:    "ready",
		Timestamp: time.Now(),
	}

	c.JSON(http.StatusOK, response)
}

// livenessCheck handles liveness probe
func (s *Server) livenessCheck(c *gin.Context) {
	response := HealthResponse{
		Status:    "alive",
		Timestamp: time.Now(),
	}

	c.JSON(http.StatusOK, response)
}

// statusCheck handles API status endpoint
func (s *Server) statusCheck(c *gin.Context) {
	uptime := time.Since(startTime)
	
	response := gin.H{
		"service": "rockerstt-backend",
		"status":  "running",
		"uptime":  uptime.String(),
		"version": "1.0.0",
		"timestamp": time.Now(),
	}

	c.JSON(http.StatusOK, response)
}

// handleToken handles the POST /api/token endpoint for Apple authentication
func (s *Server) handleToken(c *gin.Context) {
	var req TokenRequest
	
	// Bind and validate request body
	if err := c.ShouldBindJSON(&req); err != nil {
		appErr := errors.ErrInvalidRequestBody(err)
		s.errorHandler.HandleError(c, appErr)
		return
	}

	// Log authentication attempt (without exposing the token)
	s.logger.WithFields(logrus.Fields{
		"client_ip":    c.ClientIP(),
		"user_agent":   c.Request.UserAgent(),
		"token_length": len(req.IdentityToken),
	}).Info("Apple authentication attempt")

	// Authenticate with Apple
	user, err := s.authSvc.AuthenticateWithApple(req.IdentityToken)
	if err != nil {
		appErr := errors.ErrInvalidAppleToken(err)
		s.errorHandler.HandleError(c, appErr)
		return
	}

	// Generate HMAC access token using Apple user ID
	accessToken, err := s.tokenSvc.GenerateToken(user.AppleUserID)
	if err != nil {
		s.logger.WithFields(logrus.Fields{
			"user_id":       user.ID,
			"apple_user_id": user.AppleUserID,
		}).Error("Failed to generate access token")
		
		appErr := errors.ErrTokenGenerationFailed(err)
		s.errorHandler.HandleError(c, appErr)
		return
	}

	// Log successful authentication
	s.logger.WithFields(logrus.Fields{
		"user_id":       user.ID,
		"apple_user_id": user.AppleUserID,
		"email":         user.Email,
		"client_ip":     c.ClientIP(),
	}).Info("Apple authentication successful")

	// Return access token
	response := TokenResponse{
		AccessToken: accessToken,
		ExpiresIn:   s.config.Token.ExpiryMinutes * 60, // Convert minutes to seconds
	}

	c.JSON(http.StatusOK, response)
}

// WebSocket upgrader with proper configuration
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// Allow all origins for now - in production, this should be more restrictive
		return true
	},
}

// handleWebSocket handles WebSocket connections for /ws/asr endpoint
func (s *Server) handleWebSocket(c *gin.Context) {
	// Extract token from query parameters
	token := c.Query("token")
	if token == "" {
		appErr := errors.ErrMissingToken()
		s.errorHandler.HandleError(c, appErr)
		return
	}

	// Validate the HMAC token
	tokenClaims, err := s.tokenSvc.ValidateToken(token)
	if err != nil {
		appErr := errors.ErrTokenExpired(err)
		s.errorHandler.HandleError(c, appErr)
		return
	}

	// Log connection attempt
	s.logger.WithFields(logrus.Fields{
		"user_id":   tokenClaims.UserID,
		"client_ip": c.ClientIP(),
	}).Info("WebSocket connection attempt with valid token")

	// Start session tracking
	sessionID, err := s.dbSvc.StartSession(tokenClaims.UserID)
	if err != nil {
		appErr := errors.ErrSessionStartFailed(err)
		s.errorHandler.HandleError(c, appErr)
		return
	}

	// Upgrade HTTP connection to WebSocket
	clientConn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":    tokenClaims.UserID,
			"session_id": sessionID,
			"client_ip":  c.ClientIP(),
		}).Error("Failed to upgrade WebSocket connection")
		
		// End session since connection failed
		if endErr := s.dbSvc.EndSession(sessionID); endErr != nil {
			s.logger.WithError(endErr).WithField("session_id", sessionID).Error("Failed to end session after connection failure")
		}
		
		appErr := errors.ErrWebSocketUpgradeFailed(err)
		s.errorHandler.HandleError(c, appErr)
		return
	}

	// Log successful WebSocket connection
	s.logger.WithFields(logrus.Fields{
		"user_id":    tokenClaims.UserID,
		"session_id": sessionID,
		"client_ip":  c.ClientIP(),
	}).Info("WebSocket connection established")

	// Handle the WebSocket connection
	s.handleWebSocketConnection(clientConn, tokenClaims.UserID, sessionID)
}

// handleWebSocketConnection manages the lifecycle of a WebSocket connection
func (s *Server) handleWebSocketConnection(clientConn *websocket.Conn, userID, sessionID string) {
	connectionStart := time.Now()
	
	s.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"session_id": sessionID,
		"operation":  "websocket_connection_start",
	}).Info("WebSocket connection handler started")

	defer func() {
		connectionDuration := time.Since(connectionStart)
		
		// Close client connection
		clientConn.Close()
		
		// End session tracking
		if err := s.dbSvc.EndSession(sessionID); err != nil {
			s.logger.WithError(err).WithFields(logrus.Fields{
				"user_id":           userID,
				"session_id":        sessionID,
				"connection_duration": connectionDuration.String(),
				"operation":         "end_session",
			}).Error("Failed to end session")
		} else {
			s.logger.WithFields(logrus.Fields{
				"user_id":           userID,
				"session_id":        sessionID,
				"connection_duration": connectionDuration.String(),
				"operation":         "end_session",
			}).Info("Session ended successfully")
		}
		
		s.logger.WithFields(logrus.Fields{
			"user_id":           userID,
			"session_id":        sessionID,
			"connection_duration": connectionDuration.String(),
			"operation":         "websocket_connection_end",
		}).Info("WebSocket connection handler completed")
	}()

	// Connect to FunASR server
	funasrURL, err := url.Parse(s.config.FunASR.URL)
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"funasr_url": s.config.FunASR.URL,
			"user_id":    userID,
			"session_id": sessionID,
			"operation":  "parse_funasr_url",
		}).Error("Invalid FunASR URL")
		clientConn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseInternalServerErr, "Server configuration error"))
		return
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"session_id": sessionID,
		"funasr_url": funasrURL.String(),
		"operation":  "connect_to_funasr",
	}).Info("Connecting to FunASR server")

	funasrConnectStart := time.Now()
	funasrConn, _, err := websocket.DefaultDialer.Dial(funasrURL.String(), nil)
	funasrConnectDuration := time.Since(funasrConnectStart)
	
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"user_id":      userID,
			"session_id":   sessionID,
			"funasr_url":   funasrURL.String(),
			"duration_ms":  float64(funasrConnectDuration.Nanoseconds()) / 1e6,
			"operation":    "connect_to_funasr",
		}).Error("Failed to connect to FunASR server")
		
		// Send structured error message to client before closing
		errorMsg := errors.ErrFunASRUnavailable(err).ToResponse()
		clientConn.WriteJSON(errorMsg)
		clientConn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseServiceRestart, "Speech service unavailable"))
		return
	}
	defer funasrConn.Close()

	s.logger.WithFields(logrus.Fields{
		"user_id":     userID,
		"session_id":  sessionID,
		"duration_ms": float64(funasrConnectDuration.Nanoseconds()) / 1e6,
		"operation":   "connect_to_funasr",
	}).Info("Connected to FunASR server, starting proxy")

	// Start bidirectional proxy
	s.proxyWebSocketConnections(clientConn, funasrConn, userID, sessionID)
}

// proxyWebSocketConnections handles bidirectional message forwarding between client and FunASR
func (s *Server) proxyWebSocketConnections(clientConn, funasrConn *websocket.Conn, userID, sessionID string) {
	proxyStart := time.Now()
	
	s.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"session_id": sessionID,
		"operation":  "websocket_proxy_start",
	}).Info("Starting WebSocket proxy")

	// Channel to signal when either connection closes
	done := make(chan struct{})
	
	// Metrics tracking
	var clientToFunasrMessages, funasrToClientMessages int64
	var clientToFunasrBytes, funasrToClientBytes int64

	// Forward messages from client to FunASR
	go func() {
		defer func() {
			select {
			case done <- struct{}{}:
			default:
			}
		}()

		s.logger.WithFields(logrus.Fields{
			"user_id":    userID,
			"session_id": sessionID,
			"direction":  "client_to_funasr",
			"operation":  "message_forwarding_start",
		}).Debug("Started client to FunASR message forwarding")

		for {
			messageType, data, err := clientConn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					s.logger.WithError(err).WithFields(logrus.Fields{
						"user_id":    userID,
						"session_id": sessionID,
						"direction":  "client_to_funasr",
						"messages_forwarded": clientToFunasrMessages,
						"bytes_forwarded": clientToFunasrBytes,
					}).Error("Client WebSocket read error")
				} else {
					s.logger.WithFields(logrus.Fields{
						"user_id":    userID,
						"session_id": sessionID,
						"direction":  "client_to_funasr",
						"messages_forwarded": clientToFunasrMessages,
						"bytes_forwarded": clientToFunasrBytes,
					}).Info("Client WebSocket connection closed")
				}
				return
			}

			// Forward message to FunASR
			if err := funasrConn.WriteMessage(messageType, data); err != nil {
				s.logger.WithError(err).WithFields(logrus.Fields{
					"user_id":      userID,
					"session_id":   sessionID,
					"direction":    "client_to_funasr",
					"message_type": messageType,
					"data_length":  len(data),
					"messages_forwarded": clientToFunasrMessages,
				}).Error("Failed to forward message to FunASR")
				return
			}

			clientToFunasrMessages++
			clientToFunasrBytes += int64(len(data))

			s.logger.WithFields(logrus.Fields{
				"user_id":      userID,
				"session_id":   sessionID,
				"direction":    "client_to_funasr",
				"message_type": messageType,
				"data_length":  len(data),
				"total_messages": clientToFunasrMessages,
				"total_bytes": clientToFunasrBytes,
			}).Debug("Forwarded message from client to FunASR")
		}
	}()

	// Forward messages from FunASR to client
	go func() {
		defer func() {
			select {
			case done <- struct{}{}:
			default:
			}
		}()

		s.logger.WithFields(logrus.Fields{
			"user_id":    userID,
			"session_id": sessionID,
			"direction":  "funasr_to_client",
			"operation":  "message_forwarding_start",
		}).Debug("Started FunASR to client message forwarding")

		for {
			messageType, data, err := funasrConn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					s.logger.WithError(err).WithFields(logrus.Fields{
						"user_id":    userID,
						"session_id": sessionID,
						"direction":  "funasr_to_client",
						"messages_forwarded": funasrToClientMessages,
						"bytes_forwarded": funasrToClientBytes,
					}).Error("FunASR WebSocket read error")
				} else {
					s.logger.WithFields(logrus.Fields{
						"user_id":    userID,
						"session_id": sessionID,
						"direction":  "funasr_to_client",
						"messages_forwarded": funasrToClientMessages,
						"bytes_forwarded": funasrToClientBytes,
					}).Info("FunASR WebSocket connection closed")
				}
				return
			}

			// Forward message to client
			if err := clientConn.WriteMessage(messageType, data); err != nil {
				s.logger.WithError(err).WithFields(logrus.Fields{
					"user_id":      userID,
					"session_id":   sessionID,
					"direction":    "funasr_to_client",
					"message_type": messageType,
					"data_length":  len(data),
					"messages_forwarded": funasrToClientMessages,
				}).Error("Failed to forward message to client")
				return
			}

			funasrToClientMessages++
			funasrToClientBytes += int64(len(data))

			s.logger.WithFields(logrus.Fields{
				"user_id":      userID,
				"session_id":   sessionID,
				"direction":    "funasr_to_client",
				"message_type": messageType,
				"data_length":  len(data),
				"total_messages": funasrToClientMessages,
				"total_bytes": funasrToClientBytes,
			}).Debug("Forwarded message from FunASR to client")
		}
	}()

	// Wait for either connection to close
	<-done
	
	proxyDuration := time.Since(proxyStart)
	
	s.logger.WithFields(logrus.Fields{
		"user_id":                userID,
		"session_id":             sessionID,
		"proxy_duration":         proxyDuration.String(),
		"client_to_funasr_messages": clientToFunasrMessages,
		"client_to_funasr_bytes":    clientToFunasrBytes,
		"funasr_to_client_messages": funasrToClientMessages,
		"funasr_to_client_bytes":    funasrToClientBytes,
		"total_messages":         clientToFunasrMessages + funasrToClientMessages,
		"total_bytes":            clientToFunasrBytes + funasrToClientBytes,
		"operation":              "websocket_proxy_end",
	}).Info("WebSocket proxy connection closed")
}