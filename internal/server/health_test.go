package server

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"rockerstt-backend/internal/config"
	"rockerstt-backend/internal/database"
	"rockerstt-backend/internal/errors"
	appLogger "rockerstt-backend/internal/logger"
)

// setupHealthTestServer creates a minimal server for health endpoint testing
func setupHealthTestServer(t *testing.T) *Server {
	gin.SetMode(gin.TestMode)

	// Create test configuration
	cfg := &config.Config{
		Logging: config.LoggingConfig{
			Level:  "error", // Reduce noise in tests
			Format: "json",
		},
	}

	// Create test database
	gormDB, err := gorm.Open(sqlite.Open("file::memory:?cache=shared&_journal_mode=WAL"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	db := &database.DB{DB: gormDB}

	// Run migrations
	migrationService := database.NewMigrationService(db)
	err = migrationService.RunMigrations()
	require.NoError(t, err)

	// Initialize services
	log := appLogger.New(cfg)
	dbSvc := database.NewService(db, log.Logger)

	// Create minimal server
	server := &Server{
		config:       cfg,
		logger:       log,
		router:       gin.New(),
		db:           db,
		dbSvc:        dbSvc,
		errorHandler: errors.NewErrorHandler(log.Logger),
	}

	// Setup health routes only
	server.router.GET("/health", server.handleHealth)
	server.router.GET("/ready", server.handleReady)
	server.router.GET("/metrics", server.handleMetrics)

	return server
}

func TestHealthEndpoints(t *testing.T) {
	server := setupHealthTestServer(t)

	t.Run("HealthEndpoint", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/health", nil)
		require.NoError(t, err)

		w := httptest.NewRecorder()
		server.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response HealthResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "healthy", response.Status)
		assert.Equal(t, "1.0.0", response.Version)
		assert.NotEmpty(t, response.Timestamp)
		assert.NotEmpty(t, response.Uptime)
	})

	t.Run("ReadyEndpoint", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/ready", nil)
		require.NoError(t, err)

		w := httptest.NewRecorder()
		server.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response ReadinessResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "ready", response.Status)
		assert.NotEmpty(t, response.Timestamp)
		assert.Contains(t, response.Checks, "database")
		assert.Equal(t, "healthy", response.Checks["database"])
		assert.Contains(t, response.Checks, "funasr")
		assert.Equal(t, "healthy", response.Checks["funasr"])
	})

	t.Run("MetricsEndpoint", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/metrics", nil)
		require.NoError(t, err)

		w := httptest.NewRecorder()
		server.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response MetricsResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.NotEmpty(t, response.Uptime)
		assert.Equal(t, int64(0), response.TotalUsers)
		assert.Equal(t, int64(0), response.TotalSessions)
		assert.Equal(t, 0, response.ActiveSessions)
		assert.Equal(t, "healthy", response.DatabaseStatus)
	})

	t.Run("MetricsWithData", func(t *testing.T) {
		// Create some test data
		user, err := server.dbSvc.GetOrCreateUser("metrics.test.user", "<EMAIL>")
		require.NoError(t, err)

		sessionID, err := server.dbSvc.StartSession(user.AppleUserID)
		require.NoError(t, err)

		// Test metrics with data
		req, err := http.NewRequest("GET", "/metrics", nil)
		require.NoError(t, err)

		w := httptest.NewRecorder()
		server.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response MetricsResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.NotEmpty(t, response.Uptime)
		assert.Equal(t, int64(1), response.TotalUsers)
		assert.Equal(t, int64(1), response.TotalSessions)
		assert.Equal(t, 1, response.ActiveSessions) // Session is still active

		// End the session and check again
		err = server.dbSvc.EndSession(sessionID)
		require.NoError(t, err)

		req, err = http.NewRequest("GET", "/metrics", nil)
		require.NoError(t, err)

		w = httptest.NewRecorder()
		server.router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, int64(1), response.TotalUsers)
		assert.Equal(t, int64(1), response.TotalSessions)
		assert.Equal(t, 0, response.ActiveSessions) // Session is now ended
	})
}
