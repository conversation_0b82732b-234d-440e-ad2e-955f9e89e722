package auth

import (
	"crypto/rand"
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/big"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Test helper functions

func createTestRSAKey(t *testing.T) (*rsa.PrivateKey, *rsa.PublicKey) {
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)
	return privateKey, &privateKey.PublicKey
}

func createMockJWKResponse(kid string, publicKey *rsa.PublicKey) string {
	// Convert RSA public key to JWK format
	nBytes := publicKey.N.Bytes()
	eBytes := big.NewInt(int64(publicKey.E)).Bytes()
	
	jwk := JWK{
		Kty: "RSA",
		Kid: kid,
		Use: "sig",
		Alg: "RS256",
		N:   base64.RawURLEncoding.EncodeToString(nBytes),
		E:   base64.RawURLEncoding.EncodeToString(eBytes),
	}
	
	jwkSet := JWKSet{
		Keys: []JWK{jwk},
	}
	
	response, _ := json.Marshal(jwkSet)
	return string(response)
}

func createMockJWKResponseMultipleKeys(keys map[string]*rsa.PublicKey) string {
	var jwks []JWK
	
	for kid, publicKey := range keys {
		nBytes := publicKey.N.Bytes()
		eBytes := big.NewInt(int64(publicKey.E)).Bytes()
		
		jwk := JWK{
			Kty: "RSA",
			Kid: kid,
			Use: "sig",
			Alg: "RS256",
			N:   base64.RawURLEncoding.EncodeToString(nBytes),
			E:   base64.RawURLEncoding.EncodeToString(eBytes),
		}
		jwks = append(jwks, jwk)
	}
	
	jwkSet := JWKSet{Keys: jwks}
	response, _ := json.Marshal(jwkSet)
	return string(response)
}

// JWKService Tests

func TestJWKService_GetPublicKey_Success(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	// Create test key
	_, publicKey := createTestRSAKey(t)
	kid := "test-key-1"
	mockResponse := createMockJWKResponse(kid, publicKey)
	
	// Create mock server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(mockResponse))
	}))
	defer server.Close()
	
	jwkService := NewJWKService(server.URL, logger)
	
	// Test getting public key
	retrievedKey, err := jwkService.GetPublicKey(kid)
	assert.NoError(t, err)
	assert.NotNil(t, retrievedKey)
	assert.Equal(t, publicKey.N, retrievedKey.N)
	assert.Equal(t, publicKey.E, retrievedKey.E)
}

func TestJWKService_GetPublicKey_CacheHit(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	_, publicKey := createTestRSAKey(t)
	kid := "cache-test-key"
	mockResponse := createMockJWKResponse(kid, publicKey)
	
	requestCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		requestCount++
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(mockResponse))
	}))
	defer server.Close()
	
	jwkService := NewJWKService(server.URL, logger)
	
	// First request should fetch from server
	key1, err := jwkService.GetPublicKey(kid)
	assert.NoError(t, err)
	assert.NotNil(t, key1)
	assert.Equal(t, 1, requestCount)
	
	// Second request should use cache
	key2, err := jwkService.GetPublicKey(kid)
	assert.NoError(t, err)
	assert.NotNil(t, key2)
	assert.Equal(t, 1, requestCount) // Should still be 1
	
	// Keys should be identical
	assert.Equal(t, key1.N, key2.N)
	assert.Equal(t, key1.E, key2.E)
}

func TestJWKService_GetPublicKey_CacheExpiry(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	_, publicKey := createTestRSAKey(t)
	kid := "expiry-test-key"
	mockResponse := createMockJWKResponse(kid, publicKey)
	
	requestCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		requestCount++
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(mockResponse))
	}))
	defer server.Close()
	
	jwkService := NewJWKService(server.URL, logger)
	jwkService.ttl = 100 * time.Millisecond // Very short TTL for testing
	
	// First request
	_, err := jwkService.GetPublicKey(kid)
	assert.NoError(t, err)
	assert.Equal(t, 1, requestCount)
	
	// Wait for cache to expire
	time.Sleep(150 * time.Millisecond)
	
	// Second request should fetch again
	_, err = jwkService.GetPublicKey(kid)
	assert.NoError(t, err)
	assert.Equal(t, 2, requestCount)
}

func TestJWKService_GetPublicKey_KeyNotFound(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	_, publicKey := createTestRSAKey(t)
	mockResponse := createMockJWKResponse("existing-key", publicKey)
	
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(mockResponse))
	}))
	defer server.Close()
	
	jwkService := NewJWKService(server.URL, logger)
	
	// Try to get non-existent key
	key, err := jwkService.GetPublicKey("non-existent-key")
	assert.Error(t, err)
	assert.Nil(t, key)
	assert.Contains(t, err.Error(), "key with kid 'non-existent-key' not found")
}

func TestJWKService_GetPublicKey_HTTPError(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte("Internal Server Error"))
	}))
	defer server.Close()
	
	jwkService := NewJWKService(server.URL, logger)
	
	key, err := jwkService.GetPublicKey("test-key")
	assert.Error(t, err)
	assert.Nil(t, key)
	assert.Contains(t, err.Error(), "Apple JWK endpoint returned status 500")
}

func TestJWKService_GetPublicKey_InvalidJSON(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("invalid json"))
	}))
	defer server.Close()
	
	jwkService := NewJWKService(server.URL, logger)
	
	key, err := jwkService.GetPublicKey("test-key")
	assert.Error(t, err)
	assert.Nil(t, key)
	assert.Contains(t, err.Error(), "failed to parse JWK response")
}

func TestJWKService_GetPublicKey_NetworkError(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	// Use invalid URL to simulate network error
	jwkService := NewJWKService("http://invalid-url-that-does-not-exist.com", logger)
	
	key, err := jwkService.GetPublicKey("test-key")
	assert.Error(t, err)
	assert.Nil(t, key)
	assert.Contains(t, err.Error(), "failed to fetch JWK keys")
}

func TestJWKService_MultipleKeys(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	// Create multiple test keys
	keys := make(map[string]*rsa.PublicKey)
	for i := 0; i < 3; i++ {
		_, publicKey := createTestRSAKey(t)
		kid := fmt.Sprintf("test-key-%d", i)
		keys[kid] = publicKey
	}
	
	mockResponse := createMockJWKResponseMultipleKeys(keys)
	
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(mockResponse))
	}))
	defer server.Close()
	
	jwkService := NewJWKService(server.URL, logger)
	
	// Test retrieving each key
	for kid, expectedKey := range keys {
		retrievedKey, err := jwkService.GetPublicKey(kid)
		assert.NoError(t, err)
		assert.NotNil(t, retrievedKey)
		assert.Equal(t, expectedKey.N, retrievedKey.N)
		assert.Equal(t, expectedKey.E, retrievedKey.E)
	}
	
	// Verify cache count
	assert.Equal(t, len(keys), jwkService.GetCachedKeyCount())
}

func TestJWKService_RefreshKeys(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	_, publicKey1 := createTestRSAKey(t)
	_, publicKey2 := createTestRSAKey(t)
	kid := "refresh-test-key"
	
	// First response
	mockResponse1 := createMockJWKResponse(kid, publicKey1)
	// Second response with different key
	mockResponse2 := createMockJWKResponse(kid, publicKey2)
	
	requestCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		requestCount++
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		if requestCount == 1 {
			w.Write([]byte(mockResponse1))
		} else {
			w.Write([]byte(mockResponse2))
		}
	}))
	defer server.Close()
	
	jwkService := NewJWKService(server.URL, logger)
	
	// Get initial key
	key1, err := jwkService.GetPublicKey(kid)
	assert.NoError(t, err)
	assert.Equal(t, publicKey1.N, key1.N)
	
	// Force refresh
	err = jwkService.RefreshKeys()
	assert.NoError(t, err)
	
	// Get key again - should be the new key
	key2, err := jwkService.GetPublicKey(kid)
	assert.NoError(t, err)
	assert.Equal(t, publicKey2.N, key2.N)
	assert.NotEqual(t, key1.N, key2.N)
}

func TestJWKService_NonRSAKeys(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	// Create JWK response with non-RSA key
	jwkSet := JWKSet{
		Keys: []JWK{
			{
				Kty: "EC", // Elliptic Curve key, not RSA
				Kid: "ec-key",
				Use: "sig",
				Alg: "ES256",
			},
			{
				Kty: "RSA",
				Kid: "rsa-key",
				Use: "sig",
				Alg: "RS256",
				N:   "invalid-base64!@#",
				E:   "AQAB",
			},
		},
	}
	
	response, _ := json.Marshal(jwkSet)
	
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write(response)
	}))
	defer server.Close()
	
	jwkService := NewJWKService(server.URL, logger)
	
	// Should only cache RSA keys
	err := jwkService.RefreshKeys()
	assert.NoError(t, err)
	
	// EC key should not be found
	key, err := jwkService.GetPublicKey("ec-key")
	assert.Error(t, err)
	assert.Nil(t, key)
	
	// RSA key should fail due to invalid N value, but that's expected
	key, err = jwkService.GetPublicKey("rsa-key")
	assert.Error(t, err)
	assert.Nil(t, key)
}

func TestJWKService_InvalidRSAKey(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	// Create JWK with invalid RSA parameters
	jwkSet := JWKSet{
		Keys: []JWK{
			{
				Kty: "RSA",
				Kid: "invalid-rsa-key",
				Use: "sig",
				Alg: "RS256",
				N:   "invalid-base64!@#",
				E:   "AQAB",
			},
		},
	}
	
	response, _ := json.Marshal(jwkSet)
	
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write(response)
	}))
	defer server.Close()
	
	jwkService := NewJWKService(server.URL, logger)
	
	// Should handle invalid RSA key gracefully
	err := jwkService.RefreshKeys()
	assert.NoError(t, err)
	
	// Invalid key should not be cached
	assert.Equal(t, 0, jwkService.GetCachedKeyCount())
	
	key, err := jwkService.GetPublicKey("invalid-rsa-key")
	assert.Error(t, err)
	assert.Nil(t, key)
}

func TestJWKService_ConcurrentAccess(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	_, publicKey := createTestRSAKey(t)
	kid := "concurrent-test-key"
	mockResponse := createMockJWKResponse(kid, publicKey)
	
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Add small delay to simulate network latency
		time.Sleep(10 * time.Millisecond)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(mockResponse))
	}))
	defer server.Close()
	
	jwkService := NewJWKService(server.URL, logger)
	
	// Make concurrent requests
	concurrency := 10
	results := make(chan error, concurrency)
	
	for i := 0; i < concurrency; i++ {
		go func() {
			key, err := jwkService.GetPublicKey(kid)
			if err != nil {
				results <- err
				return
			}
			if key == nil {
				results <- fmt.Errorf("key is nil")
				return
			}
			results <- nil
		}()
	}
	
	// Collect results
	for i := 0; i < concurrency; i++ {
		select {
		case err := <-results:
			assert.NoError(t, err)
		case <-time.After(5 * time.Second):
			t.Fatal("Timeout waiting for concurrent requests")
		}
	}
}