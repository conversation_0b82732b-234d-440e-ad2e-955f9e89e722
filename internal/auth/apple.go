package auth

import (
	"crypto/rsa"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/sirupsen/logrus"
)

// AppleTokenClaims represents the claims in an Apple ID token
type AppleTokenClaims struct {
	Sub   string `json:"sub"`   // Subject (Apple user ID)
	Email string `json:"email"` // User's email
	Exp   int64  `json:"exp"`   // Expiration time
	Iat   int64  `json:"iat"`   // Issued at time
	Iss   string `json:"iss"`   // Issuer
	Aud   string `json:"aud"`   // Audience
	jwt.RegisteredClaims
}

// JWKServiceInterface defines the interface for JWK service
type JWKServiceInterface interface {
	GetPublicKey(kid string) (*rsa.PublicKey, error)
}

// AppleTokenValidator handles validation of Apple ID tokens
type AppleTokenValidator struct {
	jwkService    JWKServiceInterface
	expectedIss   string
	expectedAud   string
	logger        *logrus.Logger
	clockSkewSecs int64
}

// NewAppleTokenValidator creates a new Apple token validator
func NewAppleTokenValidator(jwkService JWKServiceInterface, expectedAud string, logger *logrus.Logger) *AppleTokenValidator {
	return &AppleTokenValidator{
		jwkService:    jwkService,
		expectedIss:   "https://appleid.apple.com",
		expectedAud:   expectedAud,
		logger:        logger,
		clockSkewSecs: 60, // Allow 60 seconds clock skew
	}
}

// ValidateToken validates an Apple ID token and returns the claims
func (v *AppleTokenValidator) ValidateToken(tokenString string) (*AppleTokenClaims, error) {
	start := time.Now()
	tokenLength := len(tokenString)
	
	v.logger.WithFields(logrus.Fields{
		"token_length": tokenLength,
		"operation":    "validate_apple_token",
	}).Debug("Starting Apple token validation")

	// Parse the token without verification first to get the header
	token, err := jwt.ParseWithClaims(tokenString, &AppleTokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Verify the signing method
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			v.logger.WithFields(logrus.Fields{
				"signing_method": token.Header["alg"],
				"operation":      "validate_signing_method",
			}).Warn("Unexpected signing method in Apple token")
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		// Get the key ID from the header
		kid, ok := token.Header["kid"].(string)
		if !ok {
			v.logger.WithField("operation", "extract_kid").Warn("Missing or invalid kid in Apple token header")
			return nil, fmt.Errorf("missing or invalid kid in token header")
		}

		v.logger.WithFields(logrus.Fields{
			"kid":       kid,
			"operation": "fetch_public_key",
		}).Debug("Fetching Apple public key")

		// Fetch the public key using the key ID
		keyStart := time.Now()
		publicKey, err := v.jwkService.GetPublicKey(kid)
		keyDuration := time.Since(keyStart)
		
		if err != nil {
			v.logger.WithError(err).WithFields(logrus.Fields{
				"kid":         kid,
				"operation":   "fetch_public_key",
				"duration_ms": float64(keyDuration.Nanoseconds()) / 1e6,
			}).Error("Failed to get Apple public key")
			return nil, fmt.Errorf("failed to get public key: %w", err)
		}

		v.logger.WithFields(logrus.Fields{
			"kid":         kid,
			"operation":   "fetch_public_key",
			"duration_ms": float64(keyDuration.Nanoseconds()) / 1e6,
		}).Debug("Successfully fetched Apple public key")

		return publicKey, nil
	})

	if err != nil {
		v.logger.WithError(err).WithFields(logrus.Fields{
			"token_length": tokenLength,
			"operation":    "parse_token",
			"duration_ms":  float64(time.Since(start).Nanoseconds()) / 1e6,
		}).Error("Failed to parse Apple token")
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	// Extract claims
	claims, ok := token.Claims.(*AppleTokenClaims)
	if !ok {
		v.logger.WithFields(logrus.Fields{
			"operation":   "extract_claims",
			"duration_ms": float64(time.Since(start).Nanoseconds()) / 1e6,
		}).Error("Failed to extract claims from Apple token")
		return nil, fmt.Errorf("failed to extract claims")
	}

	// Validate the token
	if !token.Valid {
		v.logger.WithFields(logrus.Fields{
			"operation":   "token_validity_check",
			"duration_ms": float64(time.Since(start).Nanoseconds()) / 1e6,
		}).Error("Apple token is invalid")
		return nil, fmt.Errorf("token is invalid")
	}

	// Validate claims
	claimsStart := time.Now()
	if err := v.validateClaims(claims); err != nil {
		v.logger.WithError(err).WithFields(logrus.Fields{
			"sub":         claims.Sub,
			"email":       claims.Email,
			"exp":         claims.Exp,
			"iat":         claims.Iat,
			"iss":         claims.Iss,
			"aud":         claims.Aud,
			"operation":   "validate_claims",
			"duration_ms": float64(time.Since(claimsStart).Nanoseconds()) / 1e6,
		}).Error("Apple token claims validation failed")
		return nil, fmt.Errorf("claims validation failed: %w", err)
	}

	totalDuration := time.Since(start)
	v.logger.WithFields(logrus.Fields{
		"sub":         claims.Sub,
		"email":       claims.Email,
		"exp":         claims.Exp,
		"iat":         claims.Iat,
		"operation":   "validate_apple_token",
		"duration_ms": float64(totalDuration.Nanoseconds()) / 1e6,
	}).Info("Successfully validated Apple token")

	return claims, nil
}

// validateClaims validates the token claims
func (v *AppleTokenValidator) validateClaims(claims *AppleTokenClaims) error {
	now := time.Now().Unix()

	v.logger.WithFields(logrus.Fields{
		"sub":              claims.Sub,
		"email":            claims.Email,
		"exp":              claims.Exp,
		"iat":              claims.Iat,
		"iss":              claims.Iss,
		"aud":              claims.Aud,
		"expected_iss":     v.expectedIss,
		"expected_aud":     v.expectedAud,
		"current_time":     now,
		"clock_skew_secs":  v.clockSkewSecs,
		"operation":        "validate_claims",
	}).Debug("Validating Apple token claims")

	// Validate issuer
	if claims.Iss != v.expectedIss {
		v.logger.WithFields(logrus.Fields{
			"expected_iss": v.expectedIss,
			"actual_iss":   claims.Iss,
			"validation":   "issuer",
		}).Warn("Apple token issuer validation failed")
		return fmt.Errorf("invalid issuer: expected %s, got %s", v.expectedIss, claims.Iss)
	}

	// Validate audience
	if claims.Aud != v.expectedAud {
		v.logger.WithFields(logrus.Fields{
			"expected_aud": v.expectedAud,
			"actual_aud":   claims.Aud,
			"validation":   "audience",
		}).Warn("Apple token audience validation failed")
		return fmt.Errorf("invalid audience: expected %s, got %s", v.expectedAud, claims.Aud)
	}

	// Validate expiration time with clock skew
	if claims.Exp < (now - v.clockSkewSecs) {
		v.logger.WithFields(logrus.Fields{
			"exp":            claims.Exp,
			"now":            now,
			"clock_skew":     v.clockSkewSecs,
			"expired_by":     now - claims.Exp,
			"validation":     "expiration",
		}).Warn("Apple token has expired")
		return fmt.Errorf("token has expired: exp=%d, now=%d", claims.Exp, now)
	}

	// Validate issued at time with clock skew (token shouldn't be from the future)
	if claims.Iat > (now + v.clockSkewSecs) {
		v.logger.WithFields(logrus.Fields{
			"iat":            claims.Iat,
			"now":            now,
			"clock_skew":     v.clockSkewSecs,
			"future_by":      claims.Iat - now,
			"validation":     "issued_at",
		}).Warn("Apple token issued in the future")
		return fmt.Errorf("token issued in the future: iat=%d, now=%d", claims.Iat, now)
	}

	// Validate subject (Apple user ID) is present
	if claims.Sub == "" {
		v.logger.WithField("validation", "subject").Warn("Apple token missing subject claim")
		return fmt.Errorf("missing subject (sub) claim")
	}

	v.logger.WithFields(logrus.Fields{
		"sub":        claims.Sub,
		"email":      claims.Email,
		"validation": "all_claims",
	}).Debug("All Apple token claims validated successfully")

	return nil
}

// SetClockSkew sets the allowed clock skew in seconds
func (v *AppleTokenValidator) SetClockSkew(seconds int64) {
	v.clockSkewSecs = seconds
}