package auth

import (
	"crypto/rand"
	"crypto/rsa"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"rockerstt-backend/internal/database"
	apperrors "rockerstt-backend/internal/errors"
	"rockerstt-backend/internal/models"
)

// setupTestDatabase creates an in-memory SQLite database for testing
func setupTestDatabase(t *testing.T) database.Database {
	gormDB, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	require.NoError(t, err)

	db := &database.DB{DB: gormDB}

	// Run migrations
	migrationService := database.NewMigrationService(db)
	err = migrationService.RunMigrations()
	require.NoError(t, err)

	return db
}

// setupTestAuthService creates a complete auth service with mock JWK server
func setupTestAuthService(t *testing.T) (*AuthService, *rsa.PrivateKey, string, *httptest.Server) {
	// Generate test key pair
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)

	kid := "test-key-id"
	mockJWKResponse := createMockJWKResponseWithKey(kid, &privateKey.PublicKey)

	// Create mock JWK server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(mockJWKResponse))
	}))

	// Setup services
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	jwkService := NewJWKService(server.URL, logger)
	tokenValidator := NewAppleTokenValidator(jwkService, "com.example.app", logger)
	db := setupTestDatabase(t)
	authService := NewAuthService(tokenValidator, db, logger)

	return authService, privateKey, kid, server
}

func TestAuthService_GetOrCreateUser_NewUser(t *testing.T) {
	authService, _, _, server := setupTestAuthService(t)
	defer server.Close()

	// Test creating a new user
	user, err := authService.GetOrCreateUser("new.apple.user", "<EMAIL>")
	require.NoError(t, err)
	assert.NotNil(t, user)
	assert.Equal(t, "new.apple.user", user.AppleUserID)
	assert.Equal(t, "<EMAIL>", user.Email)
	assert.NotZero(t, user.ID)
	assert.NotZero(t, user.CreatedAt)
	assert.NotZero(t, user.UpdatedAt)
}

func TestAuthService_GetOrCreateUser_ExistingUser(t *testing.T) {
	authService, _, _, server := setupTestAuthService(t)
	defer server.Close()

	// Create user first
	firstUser, err := authService.GetOrCreateUser("existing.apple.user", "<EMAIL>")
	require.NoError(t, err)

	// Try to get the same user again with different email
	secondUser, err := authService.GetOrCreateUser("existing.apple.user", "<EMAIL>")
	require.NoError(t, err)
	
	// Should be the same user
	assert.Equal(t, firstUser.ID, secondUser.ID)
	assert.Equal(t, firstUser.AppleUserID, secondUser.AppleUserID)
	// Email should remain the same as the original since our implementation doesn't update it
	assert.Equal(t, "<EMAIL>", secondUser.Email)
}

func TestAuthService_GetOrCreateUser_EmptyAppleUserID(t *testing.T) {
	authService, _, _, server := setupTestAuthService(t)
	defer server.Close()

	// Test with empty Apple user ID
	user, err := authService.GetOrCreateUser("", "<EMAIL>")
	assert.Error(t, err)
	assert.Nil(t, user)
	assert.Contains(t, err.Error(), "Apple user ID cannot be empty")
}

func TestAuthService_AuthenticateWithApple_Success(t *testing.T) {
	authService, privateKey, kid, server := setupTestAuthService(t)
	defer server.Close()

	// Create valid Apple token
	now := time.Now().Unix()
	claims := &AppleTokenClaims{
		Sub:   "apple.user.123",
		Email: "<EMAIL>",
		Exp:   now + 3600,
		Iat:   now - 60,
		Iss:   "https://appleid.apple.com",
		Aud:   "com.example.app",
	}

	tokenString, err := createTestAppleToken(privateKey, kid, claims)
	require.NoError(t, err)

	// Authenticate with Apple
	user, err := authService.AuthenticateWithApple(tokenString)
	require.NoError(t, err)
	assert.NotNil(t, user)
	assert.Equal(t, "apple.user.123", user.AppleUserID)
	assert.Equal(t, "<EMAIL>", user.Email)
	assert.NotZero(t, user.ID)
}

func TestAuthService_AuthenticateWithApple_InvalidToken(t *testing.T) {
	authService, _, _, server := setupTestAuthService(t)
	defer server.Close()

	// Test with invalid token
	user, err := authService.AuthenticateWithApple("invalid.token.string")
	assert.Error(t, err)
	assert.Nil(t, user)
	assert.Contains(t, err.Error(), "failed to parse token")
}

func TestAuthService_AuthenticateWithApple_ExpiredToken(t *testing.T) {
	authService, privateKey, kid, server := setupTestAuthService(t)
	defer server.Close()

	// Create expired Apple token
	now := time.Now().Unix()
	claims := &AppleTokenClaims{
		Sub:   "apple.user.expired",
		Email: "<EMAIL>",
		Exp:   now - 3600, // Expired 1 hour ago
		Iat:   now - 7200, // Issued 2 hours ago
		Iss:   "https://appleid.apple.com",
		Aud:   "com.example.app",
	}

	tokenString, err := createTestAppleToken(privateKey, kid, claims)
	require.NoError(t, err)

	// Authenticate with expired token
	user, err := authService.AuthenticateWithApple(tokenString)
	assert.Error(t, err)
	assert.Nil(t, user)
	assert.Contains(t, err.Error(), "claims validation failed")
}

func TestAuthService_AuthenticateWithApple_ExistingUser(t *testing.T) {
	authService, privateKey, kid, server := setupTestAuthService(t)
	defer server.Close()

	// Create user first through direct method
	existingUser, err := authService.GetOrCreateUser("existing.apple.user", "<EMAIL>")
	require.NoError(t, err)

	// Create valid Apple token for the same user
	now := time.Now().Unix()
	claims := &AppleTokenClaims{
		Sub:   "existing.apple.user",
		Email: "<EMAIL>",
		Exp:   now + 3600,
		Iat:   now - 60,
		Iss:   "https://appleid.apple.com",
		Aud:   "com.example.app",
	}

	tokenString, err := createTestAppleToken(privateKey, kid, claims)
	require.NoError(t, err)

	// Authenticate with Apple - should return existing user
	user, err := authService.AuthenticateWithApple(tokenString)
	require.NoError(t, err)
	assert.NotNil(t, user)
	assert.Equal(t, existingUser.ID, user.ID)
	assert.Equal(t, existingUser.AppleUserID, user.AppleUserID)
	assert.Equal(t, existingUser.Email, user.Email)
}

func TestAuthService_ValidateAppleToken_Success(t *testing.T) {
	authService, privateKey, kid, server := setupTestAuthService(t)
	defer server.Close()

	// Create valid Apple token
	now := time.Now().Unix()
	claims := &AppleTokenClaims{
		Sub:   "token.validation.user",
		Email: "<EMAIL>",
		Exp:   now + 3600,
		Iat:   now - 60,
		Iss:   "https://appleid.apple.com",
		Aud:   "com.example.app",
	}

	tokenString, err := createTestAppleToken(privateKey, kid, claims)
	require.NoError(t, err)

	// Validate token
	validatedClaims, err := authService.ValidateAppleToken(tokenString)
	require.NoError(t, err)
	assert.NotNil(t, validatedClaims)
	assert.Equal(t, "token.validation.user", validatedClaims.Sub)
	assert.Equal(t, "<EMAIL>", validatedClaims.Email)
	assert.Equal(t, "https://appleid.apple.com", validatedClaims.Iss)
	assert.Equal(t, "com.example.app", validatedClaims.Aud)
}

func TestAuthService_ValidateAppleToken_InvalidToken(t *testing.T) {
	authService, _, _, server := setupTestAuthService(t)
	defer server.Close()

	// Test with invalid token
	claims, err := authService.ValidateAppleToken("invalid.token.string")
	assert.Error(t, err)
	assert.Nil(t, claims)
}

// MockDatabase is a mock implementation of the Database interface
type MockDatabase struct {
	mock.Mock
}

func (m *MockDatabase) GetUser(appleUserID string) (*models.User, error) {
	args := m.Called(appleUserID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockDatabase) CreateUser(user *models.User) error {
	args := m.Called(user)
	if args.Error(0) == nil {
		// Simulate database setting ID and timestamps
		user.ID = 123
		user.CreatedAt = time.Now()
		user.UpdatedAt = time.Now()
	}
	return args.Error(0)
}

func (m *MockDatabase) GetUserByID(id uint) (*models.User, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockDatabase) CreateSession(session *models.Session) error {
	args := m.Called(session)
	return args.Error(0)
}

func (m *MockDatabase) GetSession(sessionID string) (*models.Session, error) {
	args := m.Called(sessionID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Session), args.Error(1)
}

func (m *MockDatabase) UpdateSession(sessionID string, endTime time.Time, duration int) error {
	args := m.Called(sessionID, endTime, duration)
	return args.Error(0)
}

func (m *MockDatabase) GetUserSessions(userID string) ([]models.Session, error) {
	args := m.Called(userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]models.Session), args.Error(1)
}

func (m *MockDatabase) Ping() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockDatabase) Initialize() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockDatabase) Close() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockDatabase) GetUserCount() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockDatabase) GetSessionCount() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockDatabase) GetActiveSessionCount() (int, error) {
	args := m.Called()
	return args.Get(0).(int), args.Error(1)
}

// MockAppleTokenValidator is a mock implementation of the Apple token validator
type MockAppleTokenValidator struct {
	mock.Mock
}

func (m *MockAppleTokenValidator) ValidateToken(tokenString string) (*AppleTokenClaims, error) {
	args := m.Called(tokenString)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*AppleTokenClaims), args.Error(1)
}

// Test with mocked dependencies for better unit testing
func TestAuthService_GetOrCreateUser_WithMocks(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	t.Run("CreateNewUser_Success", func(t *testing.T) {
		mockDB := new(MockDatabase)
		mockValidator := new(MockAppleTokenValidator)
		
		// Mock database calls
		mockDB.On("GetUser", "new.apple.user").Return(nil, nil)
		mockDB.On("CreateUser", mock.AnythingOfType("*models.User")).Return(nil)
		
		authService := NewAuthService(mockValidator, mockDB, logger)

		user, err := authService.GetOrCreateUser("new.apple.user", "<EMAIL>")
		
		assert.NoError(t, err)
		assert.NotNil(t, user)
		assert.Equal(t, "new.apple.user", user.AppleUserID)
		assert.Equal(t, "<EMAIL>", user.Email)
		assert.Equal(t, uint(123), user.ID) // Mock sets ID to 123
		
		mockDB.AssertExpectations(t)
	})

	t.Run("GetExistingUser_Success", func(t *testing.T) {
		mockDB := new(MockDatabase)
		mockValidator := new(MockAppleTokenValidator)
		
		existingUser := &models.User{
			ID:          456,
			AppleUserID: "existing.apple.user",
			Email:       "<EMAIL>",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
		
		mockDB.On("GetUser", "existing.apple.user").Return(existingUser, nil)
		
		authService := NewAuthService(mockValidator, mockDB, logger)

		user, err := authService.GetOrCreateUser("existing.apple.user", "<EMAIL>")
		
		assert.NoError(t, err)
		assert.NotNil(t, user)
		assert.Equal(t, existingUser.ID, user.ID)
		assert.Equal(t, existingUser.AppleUserID, user.AppleUserID)
		assert.Equal(t, existingUser.Email, user.Email) // Email should not be updated
		
		mockDB.AssertExpectations(t)
	})

	t.Run("DatabaseError_OnGetUser", func(t *testing.T) {
		mockDB := new(MockDatabase)
		mockValidator := new(MockAppleTokenValidator)
		
		mockDB.On("GetUser", "error.apple.user").Return(nil, errors.New("database connection failed"))
		
		authService := NewAuthService(mockValidator, mockDB, logger)

		user, err := authService.GetOrCreateUser("error.apple.user", "<EMAIL>")
		
		assert.Error(t, err)
		assert.Nil(t, user)
		assert.Contains(t, err.Error(), "Failed to retrieve user from database")
		
		// Check that it's a database error
		var appErr *apperrors.AppError
		assert.True(t, errors.As(err, &appErr))
		assert.Equal(t, apperrors.ErrorTypeDatabase, appErr.Type)
		
		mockDB.AssertExpectations(t)
	})

	t.Run("DatabaseError_OnCreateUser", func(t *testing.T) {
		mockDB := new(MockDatabase)
		mockValidator := new(MockAppleTokenValidator)
		
		mockDB.On("GetUser", "create.error.user").Return(nil, nil)
		mockDB.On("CreateUser", mock.AnythingOfType("*models.User")).Return(errors.New("create user failed"))
		
		authService := NewAuthService(mockValidator, mockDB, logger)

		user, err := authService.GetOrCreateUser("create.error.user", "<EMAIL>")
		
		assert.Error(t, err)
		assert.Nil(t, user)
		assert.Contains(t, err.Error(), "Failed to create user in database")
		
		// Check that it's a database error
		var appErr *apperrors.AppError
		assert.True(t, errors.As(err, &appErr))
		assert.Equal(t, apperrors.ErrorTypeDatabase, appErr.Type)
		assert.Equal(t, apperrors.CodeUserCreationFailed, appErr.Code)
		
		mockDB.AssertExpectations(t)
	})
}

func TestAuthService_AuthenticateWithApple_WithMocks(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	t.Run("Success_NewUser", func(t *testing.T) {
		mockDB := new(MockDatabase)
		mockValidator := new(MockAppleTokenValidator)
		
		claims := &AppleTokenClaims{
			Sub:   "apple.user.new",
			Email: "<EMAIL>",
			Exp:   time.Now().Unix() + 3600,
			Iat:   time.Now().Unix() - 60,
		}
		
		mockValidator.On("ValidateToken", "valid.token").Return(claims, nil)
		mockDB.On("GetUser", "apple.user.new").Return(nil, nil)
		mockDB.On("CreateUser", mock.AnythingOfType("*models.User")).Return(nil)
		
		authService := NewAuthService(mockValidator, mockDB, logger)

		user, err := authService.AuthenticateWithApple("valid.token")
		
		assert.NoError(t, err)
		assert.NotNil(t, user)
		assert.Equal(t, "apple.user.new", user.AppleUserID)
		assert.Equal(t, "<EMAIL>", user.Email)
		
		mockValidator.AssertExpectations(t)
		mockDB.AssertExpectations(t)
	})

	t.Run("TokenValidationFailed", func(t *testing.T) {
		mockDB := new(MockDatabase)
		mockValidator := new(MockAppleTokenValidator)
		
		mockValidator.On("ValidateToken", "invalid.token").Return(nil, errors.New("token validation failed"))
		
		authService := NewAuthService(mockValidator, mockDB, logger)

		user, err := authService.AuthenticateWithApple("invalid.token")
		
		assert.Error(t, err)
		assert.Nil(t, user)
		assert.Contains(t, err.Error(), "token validation failed")
		
		// Check that it's an auth error
		var appErr *apperrors.AppError
		assert.True(t, errors.As(err, &appErr))
		assert.Equal(t, apperrors.ErrorTypeAuth, appErr.Type)
		assert.Equal(t, apperrors.CodeInvalidAppleToken, appErr.Code)
		
		mockValidator.AssertExpectations(t)
		mockDB.AssertNotCalled(t, "GetUser")
		mockDB.AssertNotCalled(t, "CreateUser")
	})

	t.Run("UserCreationFailed", func(t *testing.T) {
		mockDB := new(MockDatabase)
		mockValidator := new(MockAppleTokenValidator)
		
		claims := &AppleTokenClaims{
			Sub:   "apple.user.fail",
			Email: "<EMAIL>",
			Exp:   time.Now().Unix() + 3600,
			Iat:   time.Now().Unix() - 60,
		}
		
		mockValidator.On("ValidateToken", "valid.token").Return(claims, nil)
		mockDB.On("GetUser", "apple.user.fail").Return(nil, nil)
		mockDB.On("CreateUser", mock.AnythingOfType("*models.User")).Return(errors.New("database error"))
		
		authService := NewAuthService(mockValidator, mockDB, logger)

		user, err := authService.AuthenticateWithApple("valid.token")
		
		assert.Error(t, err)
		assert.Nil(t, user)
		assert.Contains(t, err.Error(), "Failed to create user in database")
		
		mockValidator.AssertExpectations(t)
		mockDB.AssertExpectations(t)
	})
}

func TestAuthService_ValidateAppleToken_WithMocks(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	t.Run("Success", func(t *testing.T) {
		mockDB := new(MockDatabase)
		mockValidator := new(MockAppleTokenValidator)
		
		expectedClaims := &AppleTokenClaims{
			Sub:   "apple.user.validate",
			Email: "<EMAIL>",
			Exp:   time.Now().Unix() + 3600,
			Iat:   time.Now().Unix() - 60,
		}
		
		mockValidator.On("ValidateToken", "valid.token").Return(expectedClaims, nil)
		
		authService := NewAuthService(mockValidator, mockDB, logger)

		claims, err := authService.ValidateAppleToken("valid.token")
		
		assert.NoError(t, err)
		assert.NotNil(t, claims)
		assert.Equal(t, expectedClaims.Sub, claims.Sub)
		assert.Equal(t, expectedClaims.Email, claims.Email)
		
		mockValidator.AssertExpectations(t)
	})

	t.Run("ValidationFailed", func(t *testing.T) {
		mockDB := new(MockDatabase)
		mockValidator := new(MockAppleTokenValidator)
		
		mockValidator.On("ValidateToken", "invalid.token").Return(nil, errors.New("validation failed"))
		
		authService := NewAuthService(mockValidator, mockDB, logger)

		claims, err := authService.ValidateAppleToken("invalid.token")
		
		assert.Error(t, err)
		assert.Nil(t, claims)
		assert.Contains(t, err.Error(), "validation failed")
		
		mockValidator.AssertExpectations(t)
	})
}

// Integration test that simulates the complete flow
func TestAuthService_CompleteFlow_Integration(t *testing.T) {
	authService, privateKey, kid, server := setupTestAuthService(t)
	defer server.Close()

	// Simulate multiple users authenticating
	users := []struct {
		appleUserID string
		email       string
	}{
		{"apple.user.1", "<EMAIL>"},
		{"apple.user.2", "<EMAIL>"},
		{"apple.user.3", "<EMAIL>"},
	}

	createdUsers := make([]*models.User, len(users))

	// First authentication for each user (should create new users)
	for i, userData := range users {
		now := time.Now().Unix()
		claims := &AppleTokenClaims{
			Sub:   userData.appleUserID,
			Email: userData.email,
			Exp:   now + 3600,
			Iat:   now - 60,
			Iss:   "https://appleid.apple.com",
			Aud:   "com.example.app",
		}

		tokenString, err := createTestAppleToken(privateKey, kid, claims)
		require.NoError(t, err)

		user, err := authService.AuthenticateWithApple(tokenString)
		require.NoError(t, err)
		assert.NotNil(t, user)
		assert.Equal(t, userData.appleUserID, user.AppleUserID)
		assert.Equal(t, userData.email, user.Email)
		assert.NotZero(t, user.ID)

		createdUsers[i] = user
	}

	// Second authentication for the same users (should return existing users)
	for i, userData := range users {
		now := time.Now().Unix()
		claims := &AppleTokenClaims{
			Sub:   userData.appleUserID,
			Email: userData.email,
			Exp:   now + 3600,
			Iat:   now - 60,
			Iss:   "https://appleid.apple.com",
			Aud:   "com.example.app",
		}

		tokenString, err := createTestAppleToken(privateKey, kid, claims)
		require.NoError(t, err)

		user, err := authService.AuthenticateWithApple(tokenString)
		require.NoError(t, err)
		assert.NotNil(t, user)
		
		// Should be the same user as before
		assert.Equal(t, createdUsers[i].ID, user.ID)
		assert.Equal(t, createdUsers[i].AppleUserID, user.AppleUserID)
		assert.Equal(t, createdUsers[i].Email, user.Email)
	}

	// Test error scenarios
	t.Run("ErrorScenarios", func(t *testing.T) {
		// Test with empty Apple user ID
		user, err := authService.GetOrCreateUser("", "<EMAIL>")
		assert.Error(t, err)
		assert.Nil(t, user)
		
		var appErr *apperrors.AppError
		assert.True(t, errors.As(err, &appErr))
		assert.Equal(t, apperrors.ErrorTypeValidation, appErr.Type)
		assert.Equal(t, apperrors.CodeMissingRequiredField, appErr.Code)
	})
}