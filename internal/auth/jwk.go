package auth

import (
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/big"
	"net/http"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// JW<PERSON> represents a JSON Web Key
type JWK struct {
	Kty string `json:"kty"`
	Kid string `json:"kid"`
	Use string `json:"use"`
	Alg string `json:"alg"`
	N   string `json:"n"`
	E   string `json:"e"`
}

// JWKSet represents Apple's JWK Set response
type JWKSet struct {
	Keys []JWK `json:"keys"`
}

// JWKService handles fetching and caching Apple's public keys
type JWKService struct {
	jwkURL     string
	httpClient *http.Client
	cache      map[string]*rsa.PublicKey
	cacheMutex sync.RWMutex
	lastFetch  time.Time
	ttl        time.Duration
	logger     *logrus.Logger
}

// NewJWKService creates a new JWK service instance
func NewJWKService(jwkURL string, logger *logrus.Logger) *JWKService {
	return &JWKService{
		jwkURL: jwkURL,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
		cache:  make(map[string]*rsa.PublicKey),
		ttl:    1 * time.Hour, // Cache keys for 1 hour
		logger: logger,
	}
}

// GetPublicKey retrieves a public key by kid, fetching from Apple if needed
func (j *JWKService) GetPublicKey(kid string) (*rsa.PublicKey, error) {
	start := time.Now()
	
	j.logger.WithFields(logrus.Fields{
		"kid":       kid,
		"operation": "get_public_key",
	}).Debug("Retrieving Apple public key")

	j.cacheMutex.RLock()
	cacheAge := time.Since(j.lastFetch)
	if key, exists := j.cache[kid]; exists && cacheAge < j.ttl {
		j.cacheMutex.RUnlock()
		
		j.logger.WithFields(logrus.Fields{
			"kid":         kid,
			"cache_age":   cacheAge.String(),
			"cache_hit":   true,
			"operation":   "get_public_key",
			"duration_ms": float64(time.Since(start).Nanoseconds()) / 1e6,
		}).Debug("Apple public key found in cache")
		
		return key, nil
	}
	j.cacheMutex.RUnlock()

	j.logger.WithFields(logrus.Fields{
		"kid":       kid,
		"cache_age": cacheAge.String(),
		"cache_hit": false,
		"ttl":       j.ttl.String(),
	}).Info("Apple public key not in cache or expired, fetching from Apple")

	// Need to fetch keys
	fetchStart := time.Now()
	if err := j.fetchAndCacheKeys(); err != nil {
		j.logger.WithError(err).WithFields(logrus.Fields{
			"kid":         kid,
			"operation":   "fetch_keys",
			"duration_ms": float64(time.Since(fetchStart).Nanoseconds()) / 1e6,
		}).Error("Failed to fetch JWK keys from Apple")
		return nil, fmt.Errorf("failed to fetch JWK keys: %w", err)
	}

	j.cacheMutex.RLock()
	defer j.cacheMutex.RUnlock()
	
	key, exists := j.cache[kid]
	if !exists {
		j.logger.WithFields(logrus.Fields{
			"kid":           kid,
			"available_kids": j.getAvailableKids(),
			"operation":     "get_public_key",
			"duration_ms":   float64(time.Since(start).Nanoseconds()) / 1e6,
		}).Error("Apple public key not found after fetching")
		return nil, fmt.Errorf("key with kid '%s' not found", kid)
	}
	
	j.logger.WithFields(logrus.Fields{
		"kid":         kid,
		"operation":   "get_public_key",
		"duration_ms": float64(time.Since(start).Nanoseconds()) / 1e6,
	}).Debug("Apple public key retrieved successfully")
	
	return key, nil
}

// getAvailableKids returns a list of available key IDs (for logging)
func (j *JWKService) getAvailableKids() []string {
	kids := make([]string, 0, len(j.cache))
	for kid := range j.cache {
		kids = append(kids, kid)
	}
	return kids
}

// fetchAndCacheKeys fetches keys from Apple's JWK endpoint and caches them
func (j *JWKService) fetchAndCacheKeys() error {
	start := time.Now()
	
	j.logger.WithFields(logrus.Fields{
		"jwk_url":   j.jwkURL,
		"operation": "fetch_jwk_keys",
	}).Info("Fetching Apple JWK keys")
	
	httpStart := time.Now()
	resp, err := j.httpClient.Get(j.jwkURL)
	httpDuration := time.Since(httpStart)
	
	if err != nil {
		j.logger.WithError(err).WithFields(logrus.Fields{
			"jwk_url":     j.jwkURL,
			"operation":   "http_request",
			"duration_ms": float64(httpDuration.Nanoseconds()) / 1e6,
		}).Error("Failed to fetch JWK keys from Apple")
		return fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	j.logger.WithFields(logrus.Fields{
		"status_code": resp.StatusCode,
		"operation":   "http_request",
		"duration_ms": float64(httpDuration.Nanoseconds()) / 1e6,
	}).Debug("Received response from Apple JWK endpoint")

	if resp.StatusCode != http.StatusOK {
		j.logger.WithFields(logrus.Fields{
			"status_code": resp.StatusCode,
			"operation":   "http_request",
			"duration_ms": float64(httpDuration.Nanoseconds()) / 1e6,
		}).Error("Apple JWK endpoint returned non-200 status")
		return fmt.Errorf("Apple JWK endpoint returned status %d", resp.StatusCode)
	}

	readStart := time.Now()
	body, err := io.ReadAll(resp.Body)
	readDuration := time.Since(readStart)
	
	if err != nil {
		j.logger.WithError(err).WithFields(logrus.Fields{
			"operation":   "read_response",
			"duration_ms": float64(readDuration.Nanoseconds()) / 1e6,
		}).Error("Failed to read JWK response body")
		return fmt.Errorf("failed to read response body: %w", err)
	}

	j.logger.WithFields(logrus.Fields{
		"response_size": len(body),
		"operation":     "read_response",
		"duration_ms":   float64(readDuration.Nanoseconds()) / 1e6,
	}).Debug("Read JWK response body")

	parseStart := time.Now()
	var jwkSet JWKSet
	if err := json.Unmarshal(body, &jwkSet); err != nil {
		j.logger.WithError(err).WithFields(logrus.Fields{
			"response_size": len(body),
			"operation":     "parse_response",
			"duration_ms":   float64(time.Since(parseStart).Nanoseconds()) / 1e6,
		}).Error("Failed to parse JWK response")
		return fmt.Errorf("failed to parse JWK response: %w", err)
	}

	j.logger.WithFields(logrus.Fields{
		"key_count":   len(jwkSet.Keys),
		"operation":   "parse_response",
		"duration_ms": float64(time.Since(parseStart).Nanoseconds()) / 1e6,
	}).Debug("Parsed JWK response")

	j.cacheMutex.Lock()
	defer j.cacheMutex.Unlock()

	// Clear existing cache
	oldKeyCount := len(j.cache)
	j.cache = make(map[string]*rsa.PublicKey)

	// Parse and cache each key
	cacheStart := time.Now()
	successCount := 0
	skipCount := 0
	errorCount := 0
	
	for _, jwk := range jwkSet.Keys {
		if jwk.Kty != "RSA" {
			skipCount++
			j.logger.WithFields(logrus.Fields{
				"kid": jwk.Kid,
				"kty": jwk.Kty,
			}).Debug("Skipping non-RSA key")
			continue // Skip non-RSA keys
		}

		publicKey, err := j.parseRSAPublicKey(jwk)
		if err != nil {
			errorCount++
			j.logger.WithError(err).WithFields(logrus.Fields{
				"kid": jwk.Kid,
				"kty": jwk.Kty,
				"alg": jwk.Alg,
			}).Warn("Failed to parse RSA public key")
			continue
		}

		j.cache[jwk.Kid] = publicKey
		successCount++
		j.logger.WithFields(logrus.Fields{
			"kid": jwk.Kid,
			"alg": jwk.Alg,
		}).Debug("Cached RSA public key")
	}

	cacheDuration := time.Since(cacheStart)
	j.lastFetch = time.Now()
	totalDuration := time.Since(start)
	
	j.logger.WithFields(logrus.Fields{
		"old_key_count":    oldKeyCount,
		"new_key_count":    len(j.cache),
		"success_count":    successCount,
		"skip_count":       skipCount,
		"error_count":      errorCount,
		"cache_duration_ms": float64(cacheDuration.Nanoseconds()) / 1e6,
		"total_duration_ms": float64(totalDuration.Nanoseconds()) / 1e6,
		"operation":        "cache_keys",
	}).Info("Successfully cached Apple JWK keys")
	
	return nil
}

// parseRSAPublicKey converts a JWK to an RSA public key
func (j *JWKService) parseRSAPublicKey(jwk JWK) (*rsa.PublicKey, error) {
	// Decode the modulus (n) from base64url
	nBytes, err := base64.RawURLEncoding.DecodeString(jwk.N)
	if err != nil {
		return nil, fmt.Errorf("failed to decode modulus: %w", err)
	}

	// Decode the exponent (e) from base64url
	eBytes, err := base64.RawURLEncoding.DecodeString(jwk.E)
	if err != nil {
		return nil, fmt.Errorf("failed to decode exponent: %w", err)
	}

	// Convert bytes to big integers
	n := new(big.Int).SetBytes(nBytes)
	e := new(big.Int).SetBytes(eBytes)

	// Create RSA public key
	publicKey := &rsa.PublicKey{
		N: n,
		E: int(e.Int64()),
	}

	return publicKey, nil
}

// RefreshKeys forces a refresh of the cached keys
func (j *JWKService) RefreshKeys() error {
	return j.fetchAndCacheKeys()
}

// GetCachedKeyCount returns the number of cached keys (for testing/monitoring)
func (j *JWKService) GetCachedKeyCount() int {
	j.cacheMutex.RLock()
	defer j.cacheMutex.RUnlock()
	return len(j.cache)
}