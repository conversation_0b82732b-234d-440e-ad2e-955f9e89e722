package auth

import (
	"crypto/rand"
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/big"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// Mock JWK Service for testing Apple token validator
type MockJWKService struct {
	mock.Mock
}

func (m *MockJWKService) GetPublicKey(kid string) (*rsa.PublicKey, error) {
	args := m.Called(kid)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*rsa.PublicKey), args.Error(1)
}

// Helper function to create test Apple tokens
func createTestAppleToken(privateKey *rsa.PrivateKey, kid string, claims *AppleTokenClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	token.Header["kid"] = kid
	return token.SignedString(privateKey)
}

// Helper function to create mock JWK response with key
func createMockJWKResponseWithKey(kid string, publicKey *rsa.PublicKey) string {
	nBytes := publicKey.N.Bytes()
	eBytes := big.NewInt(int64(publicKey.E)).Bytes()
	
	jwk := JWK{
		Kty: "RSA",
		Kid: kid,
		Use: "sig",
		Alg: "RS256",
		N:   base64.RawURLEncoding.EncodeToString(nBytes),
		E:   base64.RawURLEncoding.EncodeToString(eBytes),
	}
	
	jwkSet := JWKSet{Keys: []JWK{jwk}}
	response, _ := json.Marshal(jwkSet)
	return string(response)
}

// AppleTokenValidator Tests

func TestAppleTokenValidator_ValidateToken_Success(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	// Create test key pair
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)
	publicKey := &privateKey.PublicKey
	
	kid := "test-key-id"
	expectedAud := "com.example.app"
	
	// Create mock JWK service
	mockJWKService := new(MockJWKService)
	mockJWKService.On("GetPublicKey", kid).Return(publicKey, nil)
	
	validator := NewAppleTokenValidator(mockJWKService, expectedAud, logger)
	
	// Create valid token claims
	now := time.Now().Unix()
	claims := &AppleTokenClaims{
		Sub:   "apple.user.123",
		Email: "<EMAIL>",
		Exp:   now + 3600, // Expires in 1 hour
		Iat:   now - 60,   // Issued 1 minute ago
		Iss:   "https://appleid.apple.com",
		Aud:   expectedAud,
	}
	
	tokenString, err := createTestAppleToken(privateKey, kid, claims)
	require.NoError(t, err)
	
	// Validate token
	validatedClaims, err := validator.ValidateToken(tokenString)
	assert.NoError(t, err)
	assert.NotNil(t, validatedClaims)
	assert.Equal(t, claims.Sub, validatedClaims.Sub)
	assert.Equal(t, claims.Email, validatedClaims.Email)
	assert.Equal(t, claims.Exp, validatedClaims.Exp)
	assert.Equal(t, claims.Iat, validatedClaims.Iat)
	assert.Equal(t, claims.Iss, validatedClaims.Iss)
	assert.Equal(t, claims.Aud, validatedClaims.Aud)
	
	mockJWKService.AssertExpectations(t)
}

func TestAppleTokenValidator_ValidateToken_ExpiredToken(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)
	publicKey := &privateKey.PublicKey
	
	kid := "test-key-id"
	expectedAud := "com.example.app"
	
	mockJWKService := new(MockJWKService)
	mockJWKService.On("GetPublicKey", kid).Return(publicKey, nil)
	
	validator := NewAppleTokenValidator(mockJWKService, expectedAud, logger)
	
	// Create expired token
	now := time.Now().Unix()
	claims := &AppleTokenClaims{
		Sub:   "apple.user.expired",
		Email: "<EMAIL>",
		Exp:   now - 3600, // Expired 1 hour ago
		Iat:   now - 7200, // Issued 2 hours ago
		Iss:   "https://appleid.apple.com",
		Aud:   expectedAud,
	}
	
	tokenString, err := createTestAppleToken(privateKey, kid, claims)
	require.NoError(t, err)
	
	// Should fail validation
	validatedClaims, err := validator.ValidateToken(tokenString)
	assert.Error(t, err)
	assert.Nil(t, validatedClaims)
	assert.Contains(t, err.Error(), "token has expired")
	
	mockJWKService.AssertExpectations(t)
}

func TestAppleTokenValidator_ValidateToken_FutureIssuedAt(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)
	publicKey := &privateKey.PublicKey
	
	kid := "test-key-id"
	expectedAud := "com.example.app"
	
	mockJWKService := new(MockJWKService)
	mockJWKService.On("GetPublicKey", kid).Return(publicKey, nil)
	
	validator := NewAppleTokenValidator(mockJWKService, expectedAud, logger)
	
	// Create token with future issued at time
	now := time.Now().Unix()
	claims := &AppleTokenClaims{
		Sub:   "apple.user.future",
		Email: "<EMAIL>",
		Exp:   now + 7200, // Expires in 2 hours
		Iat:   now + 3600, // Issued 1 hour in the future
		Iss:   "https://appleid.apple.com",
		Aud:   expectedAud,
	}
	
	tokenString, err := createTestAppleToken(privateKey, kid, claims)
	require.NoError(t, err)
	
	// Should fail validation
	validatedClaims, err := validator.ValidateToken(tokenString)
	assert.Error(t, err)
	assert.Nil(t, validatedClaims)
	assert.Contains(t, err.Error(), "token issued in the future")
	
	mockJWKService.AssertExpectations(t)
}

func TestAppleTokenValidator_ValidateToken_InvalidIssuer(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)
	publicKey := &privateKey.PublicKey
	
	kid := "test-key-id"
	expectedAud := "com.example.app"
	
	mockJWKService := new(MockJWKService)
	mockJWKService.On("GetPublicKey", kid).Return(publicKey, nil)
	
	validator := NewAppleTokenValidator(mockJWKService, expectedAud, logger)
	
	// Create token with invalid issuer
	now := time.Now().Unix()
	claims := &AppleTokenClaims{
		Sub:   "apple.user.badiss",
		Email: "<EMAIL>",
		Exp:   now + 3600,
		Iat:   now - 60,
		Iss:   "https://evil.com", // Invalid issuer
		Aud:   expectedAud,
	}
	
	tokenString, err := createTestAppleToken(privateKey, kid, claims)
	require.NoError(t, err)
	
	// Should fail validation
	validatedClaims, err := validator.ValidateToken(tokenString)
	assert.Error(t, err)
	assert.Nil(t, validatedClaims)
	assert.Contains(t, err.Error(), "invalid issuer")
	
	mockJWKService.AssertExpectations(t)
}

func TestAppleTokenValidator_ValidateToken_InvalidAudience(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)
	publicKey := &privateKey.PublicKey
	
	kid := "test-key-id"
	expectedAud := "com.example.app"
	
	mockJWKService := new(MockJWKService)
	mockJWKService.On("GetPublicKey", kid).Return(publicKey, nil)
	
	validator := NewAppleTokenValidator(mockJWKService, expectedAud, logger)
	
	// Create token with invalid audience
	now := time.Now().Unix()
	claims := &AppleTokenClaims{
		Sub:   "apple.user.badaud",
		Email: "<EMAIL>",
		Exp:   now + 3600,
		Iat:   now - 60,
		Iss:   "https://appleid.apple.com",
		Aud:   "com.evil.app", // Invalid audience
	}
	
	tokenString, err := createTestAppleToken(privateKey, kid, claims)
	require.NoError(t, err)
	
	// Should fail validation
	validatedClaims, err := validator.ValidateToken(tokenString)
	assert.Error(t, err)
	assert.Nil(t, validatedClaims)
	assert.Contains(t, err.Error(), "invalid audience")
	
	mockJWKService.AssertExpectations(t)
}

func TestAppleTokenValidator_ValidateToken_MissingSubject(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)
	publicKey := &privateKey.PublicKey
	
	kid := "test-key-id"
	expectedAud := "com.example.app"
	
	mockJWKService := new(MockJWKService)
	mockJWKService.On("GetPublicKey", kid).Return(publicKey, nil)
	
	validator := NewAppleTokenValidator(mockJWKService, expectedAud, logger)
	
	// Create token with missing subject
	now := time.Now().Unix()
	claims := &AppleTokenClaims{
		Sub:   "", // Empty subject
		Email: "<EMAIL>",
		Exp:   now + 3600,
		Iat:   now - 60,
		Iss:   "https://appleid.apple.com",
		Aud:   expectedAud,
	}
	
	tokenString, err := createTestAppleToken(privateKey, kid, claims)
	require.NoError(t, err)
	
	// Should fail validation
	validatedClaims, err := validator.ValidateToken(tokenString)
	assert.Error(t, err)
	assert.Nil(t, validatedClaims)
	assert.Contains(t, err.Error(), "missing subject")
	
	mockJWKService.AssertExpectations(t)
}

func TestAppleTokenValidator_ValidateToken_MissingKid(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)
	
	expectedAud := "com.example.app"
	mockJWKService := new(MockJWKService)
	
	validator := NewAppleTokenValidator(mockJWKService, expectedAud, logger)
	
	// Create token without kid in header
	now := time.Now().Unix()
	claims := &AppleTokenClaims{
		Sub:   "apple.user.nokid",
		Email: "<EMAIL>",
		Exp:   now + 3600,
		Iat:   now - 60,
		Iss:   "https://appleid.apple.com",
		Aud:   expectedAud,
	}
	
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	// Don't set kid in header
	tokenString, err := token.SignedString(privateKey)
	require.NoError(t, err)
	
	// Should fail validation
	validatedClaims, err := validator.ValidateToken(tokenString)
	assert.Error(t, err)
	assert.Nil(t, validatedClaims)
	assert.Contains(t, err.Error(), "missing or invalid kid")
	
	// JWK service should not be called
	mockJWKService.AssertNotCalled(t, "GetPublicKey")
}

func TestAppleTokenValidator_ValidateToken_JWKServiceError(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)
	
	kid := "test-key-id"
	expectedAud := "com.example.app"
	
	mockJWKService := new(MockJWKService)
	mockJWKService.On("GetPublicKey", kid).Return(nil, fmt.Errorf("JWK service error"))
	
	validator := NewAppleTokenValidator(mockJWKService, expectedAud, logger)
	
	// Create valid token
	now := time.Now().Unix()
	claims := &AppleTokenClaims{
		Sub:   "apple.user.jwkerror",
		Email: "<EMAIL>",
		Exp:   now + 3600,
		Iat:   now - 60,
		Iss:   "https://appleid.apple.com",
		Aud:   expectedAud,
	}
	
	tokenString, err := createTestAppleToken(privateKey, kid, claims)
	require.NoError(t, err)
	
	// Should fail validation due to JWK service error
	validatedClaims, err := validator.ValidateToken(tokenString)
	assert.Error(t, err)
	assert.Nil(t, validatedClaims)
	assert.Contains(t, err.Error(), "failed to get public key")
	
	mockJWKService.AssertExpectations(t)
}

func TestAppleTokenValidator_ValidateToken_WrongSigningMethod(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	kid := "test-key-id"
	expectedAud := "com.example.app"
	
	mockJWKService := new(MockJWKService)
	validator := NewAppleTokenValidator(mockJWKService, expectedAud, logger)
	
	// Create token with HMAC signing method instead of RSA
	now := time.Now().Unix()
	claims := &AppleTokenClaims{
		Sub:   "apple.user.hmac",
		Email: "<EMAIL>",
		Exp:   now + 3600,
		Iat:   now - 60,
		Iss:   "https://appleid.apple.com",
		Aud:   expectedAud,
	}
	
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims) // HMAC instead of RSA
	token.Header["kid"] = kid
	tokenString, err := token.SignedString([]byte("secret"))
	require.NoError(t, err)
	
	// Should fail validation due to wrong signing method
	validatedClaims, err := validator.ValidateToken(tokenString)
	assert.Error(t, err)
	assert.Nil(t, validatedClaims)
	assert.Contains(t, err.Error(), "unexpected signing method")
	
	// JWK service should not be called
	mockJWKService.AssertNotCalled(t, "GetPublicKey")
}

func TestAppleTokenValidator_ValidateToken_MalformedToken(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	expectedAud := "com.example.app"
	mockJWKService := new(MockJWKService)
	validator := NewAppleTokenValidator(mockJWKService, expectedAud, logger)
	
	// Test various malformed tokens
	testCases := []struct {
		name  string
		token string
	}{
		{"empty token", ""},
		{"invalid format", "invalid.token"},
		{"too many parts", "part1.part2.part3.part4"},
		{"invalid base64", "invalid-base64.invalid-base64.invalid-base64"},
	}
	
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			validatedClaims, err := validator.ValidateToken(tc.token)
			assert.Error(t, err)
			assert.Nil(t, validatedClaims)
		})
	}
	
	// JWK service should not be called for malformed tokens
	mockJWKService.AssertNotCalled(t, "GetPublicKey")
}

func TestAppleTokenValidator_SetClockSkew(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)
	publicKey := &privateKey.PublicKey
	
	kid := "test-key-id"
	expectedAud := "com.example.app"
	
	mockJWKService := new(MockJWKService)
	mockJWKService.On("GetPublicKey", kid).Return(publicKey, nil)
	
	validator := NewAppleTokenValidator(mockJWKService, expectedAud, logger)
	validator.SetClockSkew(300) // 5 minutes clock skew
	
	// Create token that's slightly expired but within clock skew
	now := time.Now().Unix()
	claims := &AppleTokenClaims{
		Sub:   "apple.user.clockskew",
		Email: "<EMAIL>",
		Exp:   now - 120, // Expired 2 minutes ago (within 5 minute skew)
		Iat:   now - 3600,
		Iss:   "https://appleid.apple.com",
		Aud:   expectedAud,
	}
	
	tokenString, err := createTestAppleToken(privateKey, kid, claims)
	require.NoError(t, err)
	
	// Should pass validation due to clock skew
	validatedClaims, err := validator.ValidateToken(tokenString)
	assert.NoError(t, err)
	assert.NotNil(t, validatedClaims)
	
	mockJWKService.AssertExpectations(t)
}

func TestAppleTokenValidator_ClockSkewBoundary(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)
	publicKey := &privateKey.PublicKey
	
	kid := "test-key-id"
	expectedAud := "com.example.app"
	
	mockJWKService := new(MockJWKService)
	mockJWKService.On("GetPublicKey", kid).Return(publicKey, nil)
	
	validator := NewAppleTokenValidator(mockJWKService, expectedAud, logger)
	validator.SetClockSkew(60) // 1 minute clock skew
	
	// Create token that's expired beyond clock skew
	now := time.Now().Unix()
	claims := &AppleTokenClaims{
		Sub:   "apple.user.beyondskew",
		Email: "<EMAIL>",
		Exp:   now - 120, // Expired 2 minutes ago (beyond 1 minute skew)
		Iat:   now - 3600,
		Iss:   "https://appleid.apple.com",
		Aud:   expectedAud,
	}
	
	tokenString, err := createTestAppleToken(privateKey, kid, claims)
	require.NoError(t, err)
	
	// Should fail validation as it's beyond clock skew
	validatedClaims, err := validator.ValidateToken(tokenString)
	assert.Error(t, err)
	assert.Nil(t, validatedClaims)
	assert.Contains(t, err.Error(), "token has expired")
	
	mockJWKService.AssertExpectations(t)
}

func TestAppleTokenValidator_Integration_WithRealJWKService(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	
	// Create test key pair
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)
	publicKey := &privateKey.PublicKey
	
	kid := "integration-test-key"
	expectedAud := "com.example.app"
	mockJWKResponse := createMockJWKResponseWithKey(kid, publicKey)
	
	// Create mock JWK server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(mockJWKResponse))
	}))
	defer server.Close()
	
	// Create real JWK service
	jwkService := NewJWKService(server.URL, logger)
	validator := NewAppleTokenValidator(jwkService, expectedAud, logger)
	
	// Create valid token
	now := time.Now().Unix()
	claims := &AppleTokenClaims{
		Sub:   "apple.user.integration",
		Email: "<EMAIL>",
		Exp:   now + 3600,
		Iat:   now - 60,
		Iss:   "https://appleid.apple.com",
		Aud:   expectedAud,
	}
	
	tokenString, err := createTestAppleToken(privateKey, kid, claims)
	require.NoError(t, err)
	
	// Should successfully validate
	validatedClaims, err := validator.ValidateToken(tokenString)
	assert.NoError(t, err)
	assert.NotNil(t, validatedClaims)
	assert.Equal(t, claims.Sub, validatedClaims.Sub)
	assert.Equal(t, claims.Email, validatedClaims.Email)
}