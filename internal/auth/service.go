package auth

import (
	"rockerstt-backend/internal/database"
	"rockerstt-backend/internal/errors"
	"rockerstt-backend/internal/models"

	"github.com/sirupsen/logrus"
)

// AppleTokenValidatorInterface defines the interface for Apple token validation
type AppleTokenValidatorInterface interface {
	ValidateToken(tokenString string) (*AppleTokenClaims, error)
}

// AuthService handles authentication operations
type AuthService struct {
	tokenValidator AppleTokenValidatorInterface
	db             database.Database
	logger         *logrus.Logger
}

// NewAuthService creates a new authentication service
func NewAuthService(tokenValidator AppleTokenValidatorInterface, db database.Database, logger *logrus.Logger) *AuthService {
	return &AuthService{
		tokenValidator: tokenValidator,
		db:             db,
		logger:         logger,
	}
}

// ValidateAppleToken validates an Apple ID token and returns the claims
func (s *AuthService) ValidateAppleToken(tokenString string) (*AppleTokenClaims, error) {
	return s.tokenValidator.ValidateToken(tokenString)
}

// GetOrCreateUser retrieves an existing user or creates a new one based on Apple user ID
func (s *AuthService) GetOrCreateUser(appleUserID, email string) (*models.User, error) {
	if appleUserID == "" {
		return nil, errors.NewValidationError(
			errors.CodeMissingRequiredField,
			"Apple user ID cannot be empty",
			nil,
		)
	}

	s.logger.WithFields(logrus.Fields{
		"apple_user_id": appleUserID,
		"email":         email,
	}).Debug("Getting or creating user")

	// Try to get existing user
	user, err := s.db.GetUser(appleUserID)
	if err != nil {
		// Database error
		s.logger.WithError(err).WithField("apple_user_id", appleUserID).Error("Failed to get user from database")
		return nil, errors.NewDatabaseError(
			errors.CodeDatabaseError,
			"Failed to retrieve user from database",
			err,
		)
	}

	// Check if user was not found (database returns nil, nil for not found)
	if user == nil {
		s.logger.WithField("apple_user_id", appleUserID).Info("User not found, creating new user")
		return s.createUser(appleUserID, email)
	}

	// User exists, update email if it has changed and is not empty
	if email != "" && user.Email != email {
		s.logger.WithFields(logrus.Fields{
			"apple_user_id": appleUserID,
			"old_email":     user.Email,
			"new_email":     email,
		}).Info("Email update requested but not implemented")
		
		// Note: Email update is not implemented in the current database interface
		// The user object remains unchanged
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":       user.ID,
		"apple_user_id": user.AppleUserID,
		"email":         user.Email,
	}).Debug("Successfully retrieved existing user")

	return user, nil
}

// createUser creates a new user in the database
func (s *AuthService) createUser(appleUserID, email string) (*models.User, error) {
	user := &models.User{
		AppleUserID: appleUserID,
		Email:       email,
	}

	if err := s.db.CreateUser(user); err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"apple_user_id": appleUserID,
			"email":         email,
		}).Error("Failed to create user in database")
		return nil, errors.NewDatabaseError(
			errors.CodeUserCreationFailed,
			"Failed to create user in database",
			err,
		)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":       user.ID,
		"apple_user_id": user.AppleUserID,
		"email":         user.Email,
	}).Info("Successfully created new user")

	return user, nil
}

// updateUser updates an existing user in the database
func (s *AuthService) updateUser(user *models.User) error {
	// Since we're using GORM, we can use the Save method
	// But we need to implement this in the database interface
	// For now, we'll skip the update functionality as it's not in the interface
	s.logger.WithField("user_id", user.ID).Debug("User update skipped - not implemented in database interface")
	return nil
}

// AuthenticateWithApple performs the complete Apple authentication flow
func (s *AuthService) AuthenticateWithApple(tokenString string) (*models.User, error) {
	// Validate the Apple token
	claims, err := s.ValidateAppleToken(tokenString)
	if err != nil {
		s.logger.WithError(err).Error("Apple token validation failed")
		return nil, errors.ErrInvalidAppleToken(err)
	}

	// Get or create user
	user, err := s.GetOrCreateUser(claims.Sub, claims.Email)
	if err != nil {
		s.logger.WithError(err).WithField("apple_user_id", claims.Sub).Error("Failed to get or create user")
		// Return the error as-is since GetOrCreateUser already returns structured errors
		return nil, err
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":       user.ID,
		"apple_user_id": user.AppleUserID,
		"email":         user.Email,
	}).Info("Apple authentication successful")

	return user, nil
}