package errors

import (
	"fmt"
	"net/http"
)

// ErrorType represents different categories of errors
type ErrorType string

const (
	// Authentication errors
	ErrorTypeAuth ErrorType = "authentication_error"
	
	// Authorization errors
	ErrorTypeAuthz ErrorType = "authorization_error"
	
	// Validation errors
	ErrorTypeValidation ErrorType = "validation_error"
	
	// Server/internal errors
	ErrorTypeServer ErrorType = "server_error"
	
	// External service errors
	ErrorTypeExternal ErrorType = "external_service_error"
	
	// Database errors
	ErrorTypeDatabase ErrorType = "database_error"
	
	// WebSocket errors
	ErrorTypeWebSocket ErrorType = "websocket_error"
)

// ErrorCode represents specific error codes
type ErrorCode string

const (
	// Authentication error codes
	CodeInvalidAppleToken    ErrorCode = "INVALID_APPLE_TOKEN"
	CodeTokenExpired         ErrorCode = "TOKEN_EXPIRED"
	CodeTokenMalformed       ErrorCode = "TOKEN_MALFORMED"
	CodeInvalidCredentials   ErrorCode = "INVALID_CREDENTIALS"
	
	// Authorization error codes
	CodeInsufficientPermissions ErrorCode = "INSUFFICIENT_PERMISSIONS"
	CodeAccessDenied            ErrorCode = "ACCESS_DENIED"
	
	// Validation error codes
	CodeInvalidRequestBody   ErrorCode = "INVALID_REQUEST_BODY"
	CodeMissingRequiredField ErrorCode = "MISSING_REQUIRED_FIELD"
	CodeInvalidParameter     ErrorCode = "INVALID_PARAMETER"
	CodeMissingToken         ErrorCode = "MISSING_TOKEN"
	CodeRateLimitExceeded    ErrorCode = "RATE_LIMIT_EXCEEDED"
	
	// Server error codes
	CodeInternalError        ErrorCode = "INTERNAL_ERROR"
	CodeServiceUnavailable   ErrorCode = "SERVICE_UNAVAILABLE"
	CodeTokenGenerationFailed ErrorCode = "TOKEN_GENERATION_FAILED"
	
	// External service error codes
	CodeAppleServiceError    ErrorCode = "APPLE_SERVICE_ERROR"
	CodeFunASRUnavailable    ErrorCode = "FUNASR_UNAVAILABLE"
	
	// Database error codes
	CodeDatabaseUnavailable  ErrorCode = "DATABASE_UNAVAILABLE"
	CodeDatabaseError        ErrorCode = "DATABASE_ERROR"
	CodeUserCreationFailed   ErrorCode = "USER_CREATION_FAILED"
	CodeSessionStartFailed   ErrorCode = "SESSION_START_FAILED"
	CodeSessionEndFailed     ErrorCode = "SESSION_END_FAILED"
	
	// WebSocket error codes
	CodeWebSocketUpgradeFailed ErrorCode = "WEBSOCKET_UPGRADE_FAILED"
	CodeWebSocketProxyError    ErrorCode = "WEBSOCKET_PROXY_ERROR"
	CodeConnectionFailed       ErrorCode = "CONNECTION_FAILED"
)

// AppError represents a structured application error
type AppError struct {
	Type       ErrorType `json:"type"`
	Code       ErrorCode `json:"code"`
	Message    string    `json:"message"`
	Details    string    `json:"details,omitempty"`
	StatusCode int       `json:"-"`
	Cause      error     `json:"-"`
}

// Error implements the error interface
func (e *AppError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// Unwrap returns the underlying cause error
func (e *AppError) Unwrap() error {
	return e.Cause
}

// ErrorResponse represents the JSON error response structure
type ErrorResponse struct {
	Error   string `json:"error"`
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// ToResponse converts an AppError to an ErrorResponse
func (e *AppError) ToResponse() ErrorResponse {
	return ErrorResponse{
		Error:   string(e.Type),
		Code:    string(e.Code),
		Message: e.Message,
		Details: e.Details,
	}
}

// NewAuthError creates a new authentication error
func NewAuthError(code ErrorCode, message string, cause error) *AppError {
	statusCode := http.StatusUnauthorized
	return &AppError{
		Type:       ErrorTypeAuth,
		Code:       code,
		Message:    message,
		StatusCode: statusCode,
		Cause:      cause,
	}
}

// NewAuthzError creates a new authorization error
func NewAuthzError(code ErrorCode, message string, cause error) *AppError {
	statusCode := http.StatusForbidden
	return &AppError{
		Type:       ErrorTypeAuthz,
		Code:       code,
		Message:    message,
		StatusCode: statusCode,
		Cause:      cause,
	}
}

// NewValidationError creates a new validation error
func NewValidationError(code ErrorCode, message string, cause error) *AppError {
	statusCode := http.StatusBadRequest
	return &AppError{
		Type:       ErrorTypeValidation,
		Code:       code,
		Message:    message,
		StatusCode: statusCode,
		Cause:      cause,
	}
}

// NewServerError creates a new server error
func NewServerError(code ErrorCode, message string, cause error) *AppError {
	statusCode := http.StatusInternalServerError
	return &AppError{
		Type:       ErrorTypeServer,
		Code:       code,
		Message:    message,
		StatusCode: statusCode,
		Cause:      cause,
	}
}

// NewExternalError creates a new external service error
func NewExternalError(code ErrorCode, message string, cause error) *AppError {
	statusCode := http.StatusBadGateway
	return &AppError{
		Type:       ErrorTypeExternal,
		Code:       code,
		Message:    message,
		StatusCode: statusCode,
		Cause:      cause,
	}
}

// NewDatabaseError creates a new database error
func NewDatabaseError(code ErrorCode, message string, cause error) *AppError {
	statusCode := http.StatusServiceUnavailable
	return &AppError{
		Type:       ErrorTypeDatabase,
		Code:       code,
		Message:    message,
		StatusCode: statusCode,
		Cause:      cause,
	}
}

// NewWebSocketError creates a new WebSocket error
func NewWebSocketError(code ErrorCode, message string, cause error) *AppError {
	statusCode := http.StatusInternalServerError
	return &AppError{
		Type:       ErrorTypeWebSocket,
		Code:       code,
		Message:    message,
		StatusCode: statusCode,
		Cause:      cause,
	}
}

// Common error constructors for frequently used errors

// ErrInvalidAppleToken creates an invalid Apple token error
func ErrInvalidAppleToken(cause error) *AppError {
	return NewAuthError(
		CodeInvalidAppleToken,
		"Apple identity token is invalid or expired",
		cause,
	)
}

// ErrTokenExpired creates a token expired error
func ErrTokenExpired(cause error) *AppError {
	return NewAuthError(
		CodeTokenExpired,
		"Access token has expired",
		cause,
	)
}

// ErrMissingToken creates a missing token error
func ErrMissingToken() *AppError {
	return NewValidationError(
		CodeMissingToken,
		"Token parameter is required",
		nil,
	)
}

// ErrInvalidRequestBody creates an invalid request body error
func ErrInvalidRequestBody(cause error) *AppError {
	return NewValidationError(
		CodeInvalidRequestBody,
		"Request body is invalid or missing required fields",
		cause,
	)
}

// ErrRateLimitExceeded creates a rate limit exceeded error
func ErrRateLimitExceeded() *AppError {
	return &AppError{
		Type:       ErrorTypeValidation,
		Code:       CodeRateLimitExceeded,
		Message:    "Rate limit exceeded. Please try again later.",
		StatusCode: http.StatusTooManyRequests,
	}
}

// ErrTokenGenerationFailed creates a token generation failed error
func ErrTokenGenerationFailed(cause error) *AppError {
	return NewServerError(
		CodeTokenGenerationFailed,
		"Failed to generate access token",
		cause,
	)
}

// ErrDatabaseUnavailable creates a database unavailable error
func ErrDatabaseUnavailable(cause error) *AppError {
	return NewDatabaseError(
		CodeDatabaseUnavailable,
		"Database is not available",
		cause,
	)
}

// ErrSessionStartFailed creates a session start failed error
func ErrSessionStartFailed(cause error) *AppError {
	return NewDatabaseError(
		CodeSessionStartFailed,
		"Failed to start session",
		cause,
	)
}

// ErrFunASRUnavailable creates a FunASR unavailable error
func ErrFunASRUnavailable(cause error) *AppError {
	return NewExternalError(
		CodeFunASRUnavailable,
		"Speech service is temporarily unavailable",
		cause,
	)
}

// ErrWebSocketUpgradeFailed creates a WebSocket upgrade failed error
func ErrWebSocketUpgradeFailed(cause error) *AppError {
	return NewWebSocketError(
		CodeWebSocketUpgradeFailed,
		"Failed to upgrade connection to WebSocket",
		cause,
	)
}