package errors

import (
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// ErrorHandler provides centralized error handling for HTTP responses
type ErrorHandler struct {
	logger *logrus.Logger
}

// NewErrorHandler creates a new error handler
func NewErrorHandler(logger *logrus.Logger) *ErrorHandler {
	return &ErrorHandler{
		logger: logger,
	}
}

// HandleError processes an error and sends appropriate HTTP response
func (h *<PERSON>rror<PERSON>andler) HandleError(c *gin.Context, err error) {
	// Check if it's already an AppError
	if appErr, ok := err.(*AppError); ok {
		h.handleAppError(c, appErr)
		return
	}

	// Convert generic error to server error
	appErr := NewServerError(
		CodeInternalError,
		"An internal server error occurred",
		err,
	)
	h.handleAppError(c, appErr)
}

// handleAppError processes an AppError and sends HTTP response
func (h *<PERSON>rror<PERSON>and<PERSON>) handleAppError(c *gin.Context, appErr *AppError) {
	// Log the error with appropriate level
	logEntry := h.logger.WithFields(logrus.Fields{
		"error_type":   appErr.Type,
		"error_code":   appErr.Code,
		"status_code":  appErr.StatusCode,
		"request_path": c.Request.URL.Path,
		"method":       c.Request.Method,
		"client_ip":    c.ClientIP(),
		"user_agent":   c.Request.UserAgent(),
	})

	// Add cause error if present
	if appErr.Cause != nil {
		logEntry = logEntry.WithError(appErr.Cause)
	}

	// Log with appropriate level based on error type
	switch appErr.Type {
	case ErrorTypeAuth, ErrorTypeAuthz:
		logEntry.Warn(appErr.Message)
	case ErrorTypeValidation:
		logEntry.Info(appErr.Message)
	case ErrorTypeServer, ErrorTypeDatabase, ErrorTypeExternal, ErrorTypeWebSocket:
		logEntry.Error(appErr.Message)
	default:
		logEntry.Error(appErr.Message)
	}

	// Send HTTP response
	c.JSON(appErr.StatusCode, appErr.ToResponse())
}

// Middleware returns a Gin middleware for centralized error handling
func (h *ErrorHandler) Middleware() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if err, ok := recovered.(error); ok {
			h.HandleError(c, err)
		} else {
			// Handle non-error panics
			appErr := NewServerError(
				CodeInternalError,
				"An unexpected error occurred",
				nil,
			)
			appErr.Details = "Server panic recovered"
			h.handleAppError(c, appErr)
		}
		c.Abort()
	})
}

// AbortWithError is a helper function to abort request with an AppError
func AbortWithError(c *gin.Context, err *AppError) {
	c.Error(err)
	c.Abort()
}

// AbortWithAuthError is a helper for authentication errors
func AbortWithAuthError(c *gin.Context, code ErrorCode, message string, cause error) {
	err := NewAuthError(code, message, cause)
	AbortWithError(c, err)
}

// AbortWithValidationError is a helper for validation errors
func AbortWithValidationError(c *gin.Context, code ErrorCode, message string, cause error) {
	err := NewValidationError(code, message, cause)
	AbortWithError(c, err)
}

// AbortWithServerError is a helper for server errors
func AbortWithServerError(c *gin.Context, code ErrorCode, message string, cause error) {
	err := NewServerError(code, message, cause)
	AbortWithError(c, err)
}

// AbortWithDatabaseError is a helper for database errors
func AbortWithDatabaseError(c *gin.Context, code ErrorCode, message string, cause error) {
	err := NewDatabaseError(code, message, cause)
	AbortWithError(c, err)
}