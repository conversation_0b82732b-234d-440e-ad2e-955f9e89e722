package docker

import (
	"bufio"
	"context"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gopkg.in/yaml.v3"

	"rockerstt-backend/internal/config"
)

// DockerComposeConfig represents the structure of docker-compose.yml
type DockerComposeConfig struct {
	Services map[string]DockerService `yaml:"services"`
}

type DockerService struct {
	Image       string            `yaml:"image,omitempty"`
	Build       *DockerBuild      `yaml:"build,omitempty"`
	Environment []string          `yaml:"environment,omitempty"`
	Ports       []string          `yaml:"ports,omitempty"`
	ExtraHosts  []string          `yaml:"extra_hosts,omitempty"`
	DependsOn   map[string]interface{} `yaml:"depends_on,omitempty"`
}

type DockerBuild struct {
	Context    string `yaml:"context"`
	Dockerfile string `yaml:"dockerfile"`
}

// TestDockerfileExists verifies that the Dockerfile exists and is readable
func TestDockerfileExists(t *testing.T) {
	dockerfilePath := filepath.Join("..", "..", "Dockerfile")
	
	_, err := os.Stat(dockerfilePath)
	assert.NoError(t, err, "Dockerfile should exist")
	
	// Verify it's readable
	content, err := os.ReadFile(dockerfilePath)
	assert.NoError(t, err, "Dockerfile should be readable")
	assert.NotEmpty(t, content, "Dockerfile should not be empty")
	
	// Verify it contains the updated FunASR URL
	dockerfileContent := string(content)
	assert.Contains(t, dockerfileContent, "FUNASR_URL=ws://host.docker.internal:10096", 
		"Dockerfile should contain updated FunASR URL")
}

// TestDockerComposeConfiguration tests the docker-compose.yml configuration
func TestDockerComposeConfiguration(t *testing.T) {
	dockerComposePath := filepath.Join("..", "..", "docker-compose.yml")
	
	// Read and parse docker-compose.yml
	content, err := os.ReadFile(dockerComposePath)
	require.NoError(t, err, "docker-compose.yml should be readable")
	
	var config DockerComposeConfig
	err = yaml.Unmarshal(content, &config)
	require.NoError(t, err, "docker-compose.yml should be valid YAML")
	
	// Test API service configuration
	apiService, exists := config.Services["api"]
	require.True(t, exists, "API service should exist in docker-compose.yml")
	
	// Verify build configuration
	require.NotNil(t, apiService.Build, "API service should have build configuration")
	assert.Equal(t, ".", apiService.Build.Context, "Build context should be current directory")
	assert.Equal(t, "Dockerfile", apiService.Build.Dockerfile, "Should use Dockerfile")
	
	// Verify environment variables
	funasrURLFound := false
	for _, env := range apiService.Environment {
		if strings.Contains(env, "FUNASR_URL=ws://host.docker.internal:10096") {
			funasrURLFound = true
			break
		}
	}
	assert.True(t, funasrURLFound, "API service should have correct FUNASR_URL environment variable")
	
	// Verify extra_hosts configuration for Docker networking
	extraHostsFound := false
	for _, host := range apiService.ExtraHosts {
		if strings.Contains(host, "host.docker.internal:host-gateway") {
			extraHostsFound = true
			break
		}
	}
	assert.True(t, extraHostsFound, "API service should have extra_hosts configuration for host access")
	
	// Verify database dependency
	assert.NotNil(t, apiService.DependsOn, "API service should depend on database")
	
	// Verify database service exists
	_, dbExists := config.Services["db"]
	assert.True(t, dbExists, "Database service should exist")
	
	// Verify nginx service exists
	_, nginxExists := config.Services["nginx"]
	assert.True(t, nginxExists, "Nginx service should exist")
	
	// Verify mock FunASR service is removed
	_, funasrExists := config.Services["funasr"]
	assert.False(t, funasrExists, "Mock FunASR service should be removed from docker-compose.yml")
}

// TestProductionDockerComposeConfiguration tests the production docker-compose configuration
func TestProductionDockerComposeConfiguration(t *testing.T) {
	prodComposePath := filepath.Join("..", "..", "deploy", "docker-compose.prod.yml")
	
	// Read and parse production docker-compose.yml
	content, err := os.ReadFile(prodComposePath)
	require.NoError(t, err, "production docker-compose.yml should be readable")
	
	var config DockerComposeConfig
	err = yaml.Unmarshal(content, &config)
	require.NoError(t, err, "production docker-compose.yml should be valid YAML")
	
	// Test API service configuration
	apiService, exists := config.Services["api"]
	require.True(t, exists, "API service should exist in production docker-compose.yml")
	
	// Verify extra_hosts configuration
	extraHostsFound := false
	for _, host := range apiService.ExtraHosts {
		if strings.Contains(host, "host.docker.internal:host-gateway") {
			extraHostsFound = true
			break
		}
	}
	assert.True(t, extraHostsFound, "Production API service should have extra_hosts configuration")
}

// TestConfigurationDefaults tests that configuration defaults are correct
func TestConfigurationDefaults(t *testing.T) {
	cfg, err := config.Load()
	require.NoError(t, err, "Configuration should load successfully")
	
	// Test default FunASR URL
	expectedURL := "ws://host.docker.internal:10096"
	if os.Getenv("FUNASR_URL") == "" {
		assert.Equal(t, expectedURL, cfg.FunASR.URL, 
			"Default FunASR URL should be updated to use host.docker.internal:10096")
	}
}

// TestFunASRURLValidation tests that FunASR URL is properly formatted
func TestFunASRURLValidation(t *testing.T) {
	testCases := []struct {
		name        string
		url         string
		shouldError bool
	}{
		{
			name:        "Valid WebSocket URL with host.docker.internal",
			url:         "ws://host.docker.internal:10096",
			shouldError: false,
		},
		{
			name:        "Valid Secure WebSocket URL",
			url:         "wss://funasr.example.com:10096",
			shouldError: false,
		},
		{
			name:        "Valid localhost URL",
			url:         "ws://localhost:10096",
			shouldError: false,
		},
		{
			name:        "Invalid protocol",
			url:         "http://host.docker.internal:10096",
			shouldError: true,
		},
		{
			name:        "Invalid URL format",
			url:         "not-a-url",
			shouldError: true,
		},
	}
	
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			parsedURL, err := url.Parse(tc.url)
			
			if tc.shouldError {
				if err == nil {
					// Check if it's a valid URL but wrong scheme
					assert.NotContains(t, []string{"ws", "wss"}, parsedURL.Scheme, 
						"URL should not have WebSocket scheme for invalid cases")
				}
			} else {
				assert.NoError(t, err, "Valid URL should parse without error")
				assert.Contains(t, []string{"ws", "wss"}, parsedURL.Scheme, 
					"Valid WebSocket URL should have ws or wss scheme")
				assert.NotEmpty(t, parsedURL.Host, "Valid URL should have host")
			}
		})
	}
}

// TestEnvironmentFileConfiguration tests environment file configurations
func TestEnvironmentFileConfiguration(t *testing.T) {
	envFiles := []string{
		filepath.Join("..", "..", ".env.docker"),
		filepath.Join("..", "..", "deploy", ".env.production.template"),
	}
	
	for _, envFile := range envFiles {
		t.Run(fmt.Sprintf("Testing %s", filepath.Base(envFile)), func(t *testing.T) {
			content, err := os.ReadFile(envFile)
			require.NoError(t, err, "Environment file should be readable")
			
			envContent := string(content)
			
			// Check for FunASR URL configuration
			assert.Contains(t, envContent, "FUNASR_URL", 
				"Environment file should contain FUNASR_URL configuration")
			
			// Check that it references the correct port
			assert.Contains(t, envContent, "10096", 
				"Environment file should reference port 10096")
		})
	}
}

// TestDockerBuild tests that the Docker image builds successfully
func TestDockerBuild(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping Docker build test in short mode")
	}

	// Change to project root directory
	projectRoot := filepath.Join("..", "..")

	// Build the Docker image
	cmd := exec.Command("docker", "build", "-t", "rockerstt-backend:test", ".")
	cmd.Dir = projectRoot

	output, err := cmd.CombinedOutput()
	if err != nil {
		t.Logf("Docker build output: %s", string(output))
		t.Fatalf("Docker build failed: %v", err)
	}

	// Verify the image was created
	cmd = exec.Command("docker", "images", "rockerstt-backend:test", "--format", "{{.Repository}}:{{.Tag}}")
	output, err = cmd.Output()
	require.NoError(t, err, "Failed to list Docker images")

	imageList := strings.TrimSpace(string(output))
	assert.Contains(t, imageList, "rockerstt-backend:test", "Docker image should be created")

	// Clean up the test image
	t.Cleanup(func() {
		exec.Command("docker", "rmi", "rockerstt-backend:test").Run()
	})
}

// TestDockerComposeValidation tests that docker-compose configuration is valid
func TestDockerComposeValidation(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping Docker Compose validation test in short mode")
	}

	projectRoot := filepath.Join("..", "..")

	// Validate docker-compose.yml
	cmd := exec.Command("docker-compose", "config")
	cmd.Dir = projectRoot

	output, err := cmd.CombinedOutput()
	if err != nil {
		t.Logf("Docker Compose validation output: %s", string(output))
		t.Fatalf("Docker Compose validation failed: %v", err)
	}

	// Verify the output contains expected services
	configOutput := string(output)
	assert.Contains(t, configOutput, "api:", "Composed config should contain API service")
	assert.Contains(t, configOutput, "db:", "Composed config should contain database service")
	assert.Contains(t, configOutput, "nginx:", "Composed config should contain nginx service")
	assert.NotContains(t, configOutput, "funasr:", "Composed config should not contain mock FunASR service")
}

// TestFunASRConnectivity tests connectivity to FunASR server (if available)
func TestFunASRConnectivity(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping FunASR connectivity test in short mode")
	}

	// Test different FunASR URLs that might be available
	testURLs := []string{
		"ws://localhost:10096",
		"ws://host.docker.internal:10096",
		"ws://127.0.0.1:10096",
	}

	var lastErr error
	connected := false

	for _, testURL := range testURLs {
		t.Logf("Testing connectivity to %s", testURL)

		// Try to connect with a short timeout
		dialer := websocket.Dialer{
			HandshakeTimeout: 2 * time.Second,
		}

		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		defer cancel()

		conn, _, err := dialer.DialContext(ctx, testURL, nil)
		if err != nil {
			lastErr = err
			t.Logf("Failed to connect to %s: %v", testURL, err)
			continue
		}

		// Successfully connected
		conn.Close()
		connected = true
		t.Logf("Successfully connected to FunASR at %s", testURL)
		break
	}

	if !connected {
		t.Logf("Could not connect to any FunASR server. Last error: %v", lastErr)
		t.Log("This is expected if FunASR server is not running. Test passes as connectivity validation.")
		// Don't fail the test - this is expected in CI/CD environments
	}
}

// TestDockerNetworkConfiguration tests Docker networking setup
func TestDockerNetworkConfiguration(t *testing.T) {
	// Test that extra_hosts configuration is properly set
	dockerComposePath := filepath.Join("..", "..", "docker-compose.yml")
	content, err := os.ReadFile(dockerComposePath)
	require.NoError(t, err)

	// Verify extra_hosts is configured
	assert.Contains(t, string(content), "extra_hosts:", "Docker compose should have extra_hosts configuration")
	assert.Contains(t, string(content), "host.docker.internal:host-gateway",
		"Docker compose should map host.docker.internal to host gateway")
}

// TestMakefileDockerCommands tests that Makefile Docker commands are valid
func TestMakefileDockerCommands(t *testing.T) {
	makefilePath := filepath.Join("..", "..", "Makefile")
	content, err := os.ReadFile(makefilePath)
	require.NoError(t, err, "Makefile should be readable")

	makefileContent := string(content)

	// Verify Docker-related targets exist
	assert.Contains(t, makefileContent, "docker-build:", "Makefile should have docker-build target")
	assert.Contains(t, makefileContent, "docker-run:", "Makefile should have docker-run target")
	assert.Contains(t, makefileContent, "test-docker:", "Makefile should have test-docker target")

	// Verify the commands reference the correct image name
	assert.Contains(t, makefileContent, "rockerstt-backend:latest",
		"Makefile should reference correct image name")
}
