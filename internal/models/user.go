package models

import (
	"time"
)

// User represents a user in the system
type User struct {
	ID          uint      `gorm:"primaryKey;autoIncrement"`
	AppleUserID string    `gorm:"uniqueIndex;not null;size:255"`
	Email       string    `gorm:"index;size:255"`
	CreatedAt   time.Time `gorm:"autoCreateTime"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime"`
}

// TableName returns the table name for the User model
func (User) TableName() string {
	return "users"
}