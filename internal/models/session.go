package models

import (
	"time"
)

// Session represents a WebSocket session for billing tracking
type Session struct {
	SessionID    string     `gorm:"primaryKey;type:varchar(36);not null"`
	UserID       string     `gorm:"not null;index;size:255"`
	StartTime    time.Time  `gorm:"not null"`
	EndTime      *time.Time `gorm:"default:null"`
	DurationSecs *int       `gorm:"default:null"`
	CreatedAt    time.Time  `gorm:"autoCreateTime"`
	UpdatedAt    time.Time  `gorm:"autoUpdateTime"`
}

// TableName returns the table name for the Session model
func (Session) TableName() string {
	return "sessions"
}