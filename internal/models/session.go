package models

import (
	"time"
)

// Session represents a WebSocket session for billing tracking
type Session struct {
	SessionID    string     `gorm:"primaryKey;type:uuid"`
	UserID       string     `gorm:"not null;index"`
	StartTime    time.Time  `gorm:"not null"`
	EndTime      *time.Time
	DurationSecs *int
	CreatedAt    time.Time
	UpdatedAt    time.Time
}

// TableName returns the table name for the Session model
func (Session) TableName() string {
	return "sessions"
}