# Git
.git
.gitignore

# Documentation
README.md
*.md

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Test files
*_test.go
testdata/

# Build artifacts
server
*.exe
*.dll
*.so
*.dylib

# Temporary files
tmp/
temp/

# Environment files (these should be provided at runtime)
.env
.env.local
.env.*.local

# Docker files (not needed in build context)
Dockerfile*
docker-compose*.yml
.dockerignore

# Kiro configuration
.kiro/

# Development tools
Makefile
bin/

# Coverage reports
coverage.out
coverage.html